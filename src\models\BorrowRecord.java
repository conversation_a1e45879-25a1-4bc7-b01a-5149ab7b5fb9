package models;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * BorrowRecord model class representing book borrowing transactions
 */
public class BorrowRecord {
    
    public enum Status {
        BORROWED, RETURNED, OVERDUE
    }
    
    private int recordId;
    private int userId;
    private int bookId;
    private Date borrowDate;
    private Date dueDate;
    private Date returnDate;
    private Status status;
    private BigDecimal fineAmount;
    private boolean finePaid;
    private Timestamp createdAt;
    private Timestamp updatedAt;
    
    // Related objects (for joins)
    private User user;
    private Book book;
    
    // Default constructor
    public BorrowRecord() {
        this.status = Status.BORROWED;
        this.fineAmount = BigDecimal.ZERO;
        this.finePaid = false;
        this.borrowDate = new Date(System.currentTimeMillis());
    }
    
    // Constructor with essential fields
    public BorrowRecord(int userId, int bookId, Date dueDate) {
        this();
        this.userId = userId;
        this.bookId = bookId;
        this.dueDate = dueDate;
    }
    
    // Constructor with user and book objects
    public BorrowRecord(User user, Book book, Date dueDate) {
        this(user.getUserId(), book.getBookId(), dueDate);
        this.user = user;
        this.book = book;
    }
    
    // Full constructor
    public BorrowRecord(int recordId, int userId, int bookId, Date borrowDate, Date dueDate,
                       Date returnDate, Status status, BigDecimal fineAmount, boolean finePaid,
                       Timestamp createdAt, Timestamp updatedAt) {
        this.recordId = recordId;
        this.userId = userId;
        this.bookId = bookId;
        this.borrowDate = borrowDate;
        this.dueDate = dueDate;
        this.returnDate = returnDate;
        this.status = status;
        this.fineAmount = fineAmount;
        this.finePaid = finePaid;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Getters and Setters
    public int getRecordId() {
        return recordId;
    }
    
    public void setRecordId(int recordId) {
        this.recordId = recordId;
    }
    
    public int getUserId() {
        return userId;
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
    }
    
    public int getBookId() {
        return bookId;
    }
    
    public void setBookId(int bookId) {
        this.bookId = bookId;
    }
    
    public Date getBorrowDate() {
        return borrowDate;
    }
    
    public void setBorrowDate(Date borrowDate) {
        this.borrowDate = borrowDate;
    }
    
    public Date getDueDate() {
        return dueDate;
    }
    
    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }
    
    public Date getReturnDate() {
        return returnDate;
    }
    
    public void setReturnDate(Date returnDate) {
        this.returnDate = returnDate;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public BigDecimal getFineAmount() {
        return fineAmount;
    }
    
    public void setFineAmount(BigDecimal fineAmount) {
        this.fineAmount = fineAmount;
    }
    
    public boolean isFinePaid() {
        return finePaid;
    }
    
    public void setFinePaid(boolean finePaid) {
        this.finePaid = finePaid;
    }
    
    public Timestamp getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }
    
    public Timestamp getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public Book getBook() {
        return book;
    }
    
    public void setBook(Book book) {
        this.book = book;
    }
    
    // Utility methods
    public boolean isOverdue() {
        if (returnDate != null) return false; // Already returned
        LocalDate today = LocalDate.now();
        LocalDate due = dueDate.toLocalDate();
        return today.isAfter(due);
    }
    
    public long getDaysOverdue() {
        if (!isOverdue()) return 0;
        LocalDate today = LocalDate.now();
        LocalDate due = dueDate.toLocalDate();
        return ChronoUnit.DAYS.between(due, today);
    }
    
    public long getDaysBorrowed() {
        LocalDate start = borrowDate.toLocalDate();
        LocalDate end = returnDate != null ? returnDate.toLocalDate() : LocalDate.now();
        return ChronoUnit.DAYS.between(start, end);
    }
    
    public boolean isReturned() {
        return status == Status.RETURNED && returnDate != null;
    }
    
    public boolean isActive() {
        return status == Status.BORROWED || status == Status.OVERDUE;
    }
    
    public boolean hasFine() {
        return fineAmount != null && fineAmount.compareTo(BigDecimal.ZERO) > 0;
    }
    
    public boolean hasUnpaidFine() {
        return hasFine() && !finePaid;
    }
    
    public String getStatusDisplay() {
        switch (status) {
            case BORROWED:
                return isOverdue() ? "Overdue" : "Borrowed";
            case RETURNED:
                return "Returned";
            case OVERDUE:
                return "Overdue";
            default:
                return status.toString();
        }
    }
    
    public String getFormattedFineAmount() {
        return fineAmount != null ? "$" + fineAmount.toString() : "$0.00";
    }
    
    /**
     * Calculate fine based on days overdue and daily fine rate
     * @param dailyFineRate the fine amount per day
     * @return calculated fine amount
     */
    public BigDecimal calculateFine(BigDecimal dailyFineRate) {
        if (!isOverdue()) return BigDecimal.ZERO;
        long daysOverdue = getDaysOverdue();
        return dailyFineRate.multiply(BigDecimal.valueOf(daysOverdue));
    }
    
    /**
     * Mark the book as returned
     */
    public void markAsReturned() {
        this.returnDate = new Date(System.currentTimeMillis());
        this.status = Status.RETURNED;
    }
    
    /**
     * Mark the book as overdue
     */
    public void markAsOverdue() {
        this.status = Status.OVERDUE;
    }
    
    @Override
    public String toString() {
        return "BorrowRecord{" +
                "recordId=" + recordId +
                ", userId=" + userId +
                ", bookId=" + bookId +
                ", borrowDate=" + borrowDate +
                ", dueDate=" + dueDate +
                ", returnDate=" + returnDate +
                ", status=" + status +
                ", fineAmount=" + fineAmount +
                ", finePaid=" + finePaid +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        BorrowRecord that = (BorrowRecord) obj;
        return recordId == that.recordId;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(recordId);
    }
}
