package models;

import java.sql.Timestamp;

/**
 * DigitalAccess model class representing user access to digital books
 */
public class DigitalAccess {
    
    private int accessId;
    private int userId;
    private int bookId;
    private Timestamp accessDate;
    private Timestamp lastAccessed;
    private int readingProgress; // percentage or page number
    private String bookmarks; // JSON string of bookmarks
    private int downloadCount;
    private boolean isDownloaded;
    
    // Related objects (for joins)
    private User user;
    private Book book;
    
    // Default constructor
    public DigitalAccess() {
        this.accessDate = new Timestamp(System.currentTimeMillis());
        this.lastAccessed = new Timestamp(System.currentTimeMillis());
        this.readingProgress = 0;
        this.downloadCount = 0;
        this.isDownloaded = false;
    }
    
    // Constructor with essential fields
    public DigitalAccess(int userId, int bookId) {
        this();
        this.userId = userId;
        this.bookId = bookId;
    }
    
    // Constructor with user and book objects
    public DigitalAccess(User user, Book book) {
        this(user.getUserId(), book.getBookId());
        this.user = user;
        this.book = book;
    }
    
    // Full constructor
    public DigitalAccess(int accessId, int userId, int bookId, Timestamp accessDate,
                        Timestamp lastAccessed, int readingProgress, String bookmarks,
                        int downloadCount, boolean isDownloaded) {
        this.accessId = accessId;
        this.userId = userId;
        this.bookId = bookId;
        this.accessDate = accessDate;
        this.lastAccessed = lastAccessed;
        this.readingProgress = readingProgress;
        this.bookmarks = bookmarks;
        this.downloadCount = downloadCount;
        this.isDownloaded = isDownloaded;
    }
    
    // Getters and Setters
    public int getAccessId() {
        return accessId;
    }
    
    public void setAccessId(int accessId) {
        this.accessId = accessId;
    }
    
    public int getUserId() {
        return userId;
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
    }
    
    public int getBookId() {
        return bookId;
    }
    
    public void setBookId(int bookId) {
        this.bookId = bookId;
    }
    
    public Timestamp getAccessDate() {
        return accessDate;
    }
    
    public void setAccessDate(Timestamp accessDate) {
        this.accessDate = accessDate;
    }
    
    public Timestamp getLastAccessed() {
        return lastAccessed;
    }
    
    public void setLastAccessed(Timestamp lastAccessed) {
        this.lastAccessed = lastAccessed;
    }
    
    public int getReadingProgress() {
        return readingProgress;
    }
    
    public void setReadingProgress(int readingProgress) {
        this.readingProgress = Math.max(0, Math.min(100, readingProgress)); // Ensure 0-100 range
    }
    
    public String getBookmarks() {
        return bookmarks;
    }
    
    public void setBookmarks(String bookmarks) {
        this.bookmarks = bookmarks;
    }
    
    public int getDownloadCount() {
        return downloadCount;
    }
    
    public void setDownloadCount(int downloadCount) {
        this.downloadCount = downloadCount;
    }
    
    public boolean isDownloaded() {
        return isDownloaded;
    }
    
    public void setDownloaded(boolean downloaded) {
        isDownloaded = downloaded;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public Book getBook() {
        return book;
    }
    
    public void setBook(Book book) {
        this.book = book;
    }
    
    // Utility methods
    public void updateLastAccessed() {
        this.lastAccessed = new Timestamp(System.currentTimeMillis());
    }
    
    public void incrementDownloadCount() {
        this.downloadCount++;
        this.isDownloaded = true;
    }
    
    public boolean isRecentlyAccessed() {
        long hoursSinceLastAccess = (System.currentTimeMillis() - lastAccessed.getTime()) / (1000 * 60 * 60);
        return hoursSinceLastAccess <= 24; // Within last 24 hours
    }
    
    public String getProgressDisplay() {
        return readingProgress + "%";
    }
    
    public boolean isCompleted() {
        return readingProgress >= 100;
    }
    
    public boolean isStarted() {
        return readingProgress > 0;
    }
    
    public String getReadingStatus() {
        if (readingProgress == 0) return "Not Started";
        if (readingProgress >= 100) return "Completed";
        return "In Progress (" + readingProgress + "%)";
    }
    
    /**
     * Add a bookmark (simple implementation)
     * @param bookmark the bookmark to add
     */
    public void addBookmark(String bookmark) {
        if (bookmarks == null || bookmarks.isEmpty()) {
            bookmarks = bookmark;
        } else {
            bookmarks += "," + bookmark;
        }
    }
    
    /**
     * Get bookmarks as array
     * @return array of bookmarks
     */
    public String[] getBookmarksArray() {
        if (bookmarks == null || bookmarks.isEmpty()) {
            return new String[0];
        }
        return bookmarks.split(",");
    }
    
    @Override
    public String toString() {
        return "DigitalAccess{" +
                "accessId=" + accessId +
                ", userId=" + userId +
                ", bookId=" + bookId +
                ", accessDate=" + accessDate +
                ", lastAccessed=" + lastAccessed +
                ", readingProgress=" + readingProgress +
                ", downloadCount=" + downloadCount +
                ", isDownloaded=" + isDownloaded +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DigitalAccess that = (DigitalAccess) obj;
        return accessId == that.accessId;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(accessId);
    }
}
