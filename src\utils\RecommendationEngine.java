package utils;

import models.Book;
import models.User;
import db.DBConnection;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Utility class for generating book recommendations
 */
public class RecommendationEngine {
    
    private static final Logger LOGGER = Logger.getLogger(RecommendationEngine.class.getName());
    
    // Recommendation types
    public enum RecommendationType {
        GENRE_BASED, HISTORY_BASED, MOOD_BASED, TRENDING
    }
    
    /**
     * Get personalized recommendations for a user
     * @param userId the user ID
     * @param limit maximum number of recommendations
     * @return list of recommended books
     */
    public static List<Book> getPersonalizedRecommendations(int userId, int limit) {
        List<Book> recommendations = new ArrayList<>();
        
        // Combine different recommendation strategies
        recommendations.addAll(getGenreBasedRecommendations(userId, limit / 3));
        recommendations.addAll(getHistoryBasedRecommendations(userId, limit / 3));
        recommendations.addAll(getTrendingRecommendations(limit / 3));
        
        // Remove duplicates and limit results
        Set<Integer> seenBookIds = new HashSet<>();
        List<Book> uniqueRecommendations = new ArrayList<>();
        
        for (Book book : recommendations) {
            if (!seenBookIds.contains(book.getBookId()) && uniqueRecommendations.size() < limit) {
                seenBookIds.add(book.getBookId());
                uniqueRecommendations.add(book);
            }
        }
        
        return uniqueRecommendations;
    }
    
    /**
     * Get genre-based recommendations
     * @param userId the user ID
     * @param limit maximum number of recommendations
     * @return list of recommended books
     */
    public static List<Book> getGenreBasedRecommendations(int userId, int limit) {
        List<Book> recommendations = new ArrayList<>();
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DBConnection.getConnection();
            
            // Get user's favorite genres based on borrowing history
            String genreQuery = """
                SELECT b.genre, COUNT(*) as borrow_count
                FROM borrow_records br
                JOIN books b ON br.book_id = b.book_id
                WHERE br.user_id = ? AND b.genre IS NOT NULL
                GROUP BY b.genre
                ORDER BY borrow_count DESC
                LIMIT 3
                """;
            
            statement = connection.prepareStatement(genreQuery);
            statement.setInt(1, userId);
            resultSet = statement.executeQuery();
            
            List<String> favoriteGenres = new ArrayList<>();
            while (resultSet.next()) {
                favoriteGenres.add(resultSet.getString("genre"));
            }
            
            // If no borrowing history, use popular genres
            if (favoriteGenres.isEmpty()) {
                favoriteGenres.addAll(Arrays.asList("Fiction", "Mystery", "Science Fiction"));
            }
            
            // Get recommendations based on favorite genres
            for (String genre : favoriteGenres) {
                List<Book> genreBooks = getBooksByGenre(genre, userId, limit / favoriteGenres.size());
                recommendations.addAll(genreBooks);
            }
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get genre-based recommendations", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }
        
        return recommendations;
    }
    
    /**
     * Get history-based recommendations (collaborative filtering)
     * @param userId the user ID
     * @param limit maximum number of recommendations
     * @return list of recommended books
     */
    public static List<Book> getHistoryBasedRecommendations(int userId, int limit) {
        List<Book> recommendations = new ArrayList<>();
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DBConnection.getConnection();
            
            // Find users with similar borrowing patterns
            String similarUsersQuery = """
                SELECT br2.user_id, COUNT(*) as common_books
                FROM borrow_records br1
                JOIN borrow_records br2 ON br1.book_id = br2.book_id
                WHERE br1.user_id = ? AND br2.user_id != ?
                GROUP BY br2.user_id
                HAVING common_books >= 2
                ORDER BY common_books DESC
                LIMIT 10
                """;
            
            statement = connection.prepareStatement(similarUsersQuery);
            statement.setInt(1, userId);
            statement.setInt(2, userId);
            resultSet = statement.executeQuery();
            
            List<Integer> similarUserIds = new ArrayList<>();
            while (resultSet.next()) {
                similarUserIds.add(resultSet.getInt("user_id"));
            }
            
            if (!similarUserIds.isEmpty()) {
                // Get books borrowed by similar users but not by current user
                StringBuilder placeholders = new StringBuilder();
                for (int i = 0; i < similarUserIds.size(); i++) {
                    placeholders.append("?");
                    if (i < similarUserIds.size() - 1) placeholders.append(",");
                }
                
                String recommendationQuery = String.format("""
                    SELECT DISTINCT b.*, COUNT(*) as recommendation_score
                    FROM books b
                    JOIN borrow_records br ON b.book_id = br.book_id
                    WHERE br.user_id IN (%s)
                    AND b.book_id NOT IN (
                        SELECT book_id FROM borrow_records WHERE user_id = ?
                    )
                    AND b.is_active = TRUE
                    AND (b.available_copies > 0 OR b.digital_available = TRUE)
                    GROUP BY b.book_id
                    ORDER BY recommendation_score DESC
                    LIMIT ?
                    """, placeholders.toString());
                
                DBConnection.closeStatement(statement);
                statement = connection.prepareStatement(recommendationQuery);
                
                int paramIndex = 1;
                for (Integer similarUserId : similarUserIds) {
                    statement.setInt(paramIndex++, similarUserId);
                }
                statement.setInt(paramIndex++, userId);
                statement.setInt(paramIndex, limit);
                
                DBConnection.closeResultSet(resultSet);
                resultSet = statement.executeQuery();
                
                while (resultSet.next()) {
                    Book book = mapResultSetToBook(resultSet);
                    recommendations.add(book);
                }
            }
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get history-based recommendations", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }
        
        return recommendations;
    }
    
    /**
     * Get trending/popular books
     * @param limit maximum number of recommendations
     * @return list of trending books
     */
    public static List<Book> getTrendingRecommendations(int limit) {
        List<Book> recommendations = new ArrayList<>();
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DBConnection.getConnection();
            
            // Get most borrowed books in the last 30 days
            String trendingQuery = """
                SELECT b.*, COUNT(*) as borrow_count
                FROM books b
                JOIN borrow_records br ON b.book_id = br.book_id
                WHERE br.borrow_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                AND b.is_active = TRUE
                AND (b.available_copies > 0 OR b.digital_available = TRUE)
                GROUP BY b.book_id
                ORDER BY borrow_count DESC
                LIMIT ?
                """;
            
            statement = connection.prepareStatement(trendingQuery);
            statement.setInt(1, limit);
            resultSet = statement.executeQuery();
            
            while (resultSet.next()) {
                Book book = mapResultSetToBook(resultSet);
                recommendations.add(book);
            }
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get trending recommendations", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }
        
        return recommendations;
    }
    
    /**
     * Get mood-based recommendations
     * @param mood the user's current mood
     * @param limit maximum number of recommendations
     * @return list of recommended books
     */
    public static List<Book> getMoodBasedRecommendations(String mood, int limit) {
        Map<String, List<String>> moodToGenres = new HashMap<>();
        moodToGenres.put("happy", Arrays.asList("Comedy", "Romance", "Adventure"));
        moodToGenres.put("sad", Arrays.asList("Drama", "Literary Fiction", "Poetry"));
        moodToGenres.put("excited", Arrays.asList("Action", "Thriller", "Science Fiction"));
        moodToGenres.put("relaxed", Arrays.asList("Fiction", "Biography", "History"));
        moodToGenres.put("curious", Arrays.asList("Non-fiction", "Science", "Philosophy"));
        
        List<String> genres = moodToGenres.getOrDefault(mood.toLowerCase(), 
                                                       Arrays.asList("Fiction", "Mystery"));
        
        List<Book> recommendations = new ArrayList<>();
        for (String genre : genres) {
            List<Book> genreBooks = getBooksByGenre(genre, -1, limit / genres.size());
            recommendations.addAll(genreBooks);
        }
        
        return recommendations;
    }
    
    /**
     * Get books by genre
     * @param genre the genre to search for
     * @param excludeUserId user ID to exclude from recommendations (or -1 to include all)
     * @param limit maximum number of books
     * @return list of books
     */
    private static List<Book> getBooksByGenre(String genre, int excludeUserId, int limit) {
        List<Book> books = new ArrayList<>();
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DBConnection.getConnection();
            
            String query;
            if (excludeUserId > 0) {
                query = """
                    SELECT DISTINCT b.*
                    FROM books b
                    WHERE b.genre = ? 
                    AND b.is_active = TRUE
                    AND (b.available_copies > 0 OR b.digital_available = TRUE)
                    AND b.book_id NOT IN (
                        SELECT book_id FROM borrow_records WHERE user_id = ?
                    )
                    ORDER BY b.created_at DESC
                    LIMIT ?
                    """;
            } else {
                query = """
                    SELECT b.*
                    FROM books b
                    WHERE b.genre = ? 
                    AND b.is_active = TRUE
                    AND (b.available_copies > 0 OR b.digital_available = TRUE)
                    ORDER BY b.created_at DESC
                    LIMIT ?
                    """;
            }
            
            statement = connection.prepareStatement(query);
            statement.setString(1, genre);
            
            if (excludeUserId > 0) {
                statement.setInt(2, excludeUserId);
                statement.setInt(3, limit);
            } else {
                statement.setInt(2, limit);
            }
            
            resultSet = statement.executeQuery();
            
            while (resultSet.next()) {
                Book book = mapResultSetToBook(resultSet);
                books.add(book);
            }
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get books by genre: " + genre, e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }
        
        return books;
    }
    
    /**
     * Map ResultSet to Book object
     * @param resultSet the result set
     * @return Book object
     * @throws SQLException if mapping fails
     */
    private static Book mapResultSetToBook(ResultSet resultSet) throws SQLException {
        Book book = new Book();
        book.setBookId(resultSet.getInt("book_id"));
        book.setIsbn(resultSet.getString("isbn"));
        book.setTitle(resultSet.getString("title"));
        book.setAuthor(resultSet.getString("author"));
        book.setGenre(resultSet.getString("genre"));
        book.setPublisher(resultSet.getString("publisher"));
        book.setPublicationYear(resultSet.getInt("publication_year"));
        book.setDescription(resultSet.getString("description"));
        book.setPhysicalCopies(resultSet.getInt("physical_copies"));
        book.setAvailableCopies(resultSet.getInt("available_copies"));
        book.setDigitalAvailable(resultSet.getBoolean("digital_available"));
        book.setDigitalFilePath(resultSet.getString("digital_file_path"));
        
        String digitalFileType = resultSet.getString("digital_file_type");
        if (digitalFileType != null) {
            book.setDigitalFileType(Book.DigitalFileType.valueOf(digitalFileType));
        }
        
        book.setCoverImagePath(resultSet.getString("cover_image_path"));
        book.setPrice(resultSet.getBigDecimal("price"));
        book.setActive(resultSet.getBoolean("is_active"));
        book.setCreatedAt(resultSet.getTimestamp("created_at"));
        book.setUpdatedAt(resultSet.getTimestamp("updated_at"));
        
        return book;
    }
    
    /**
     * Save recommendation to database for tracking
     * @param userId the user ID
     * @param bookId the book ID
     * @param type the recommendation type
     * @param score the recommendation score
     * @return true if saved successfully
     */
    public static boolean saveRecommendation(int userId, int bookId, RecommendationType type, double score) {
        Connection connection = null;
        PreparedStatement statement = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = """
                INSERT INTO recommendations (user_id, book_id, recommendation_type, recommendation_score)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                recommendation_score = VALUES(recommendation_score),
                created_at = CURRENT_TIMESTAMP
                """;
            
            statement = connection.prepareStatement(sql);
            statement.setInt(1, userId);
            statement.setInt(2, bookId);
            statement.setString(3, type.toString());
            statement.setDouble(4, score);
            
            return statement.executeUpdate() > 0;
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to save recommendation", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }
}
