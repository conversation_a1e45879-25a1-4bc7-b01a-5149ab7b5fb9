package utils;

import models.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.logging.Logger;

/**
 * Utility class for managing user sessions
 */
public class SessionManager {
    
    private static final Logger LOGGER = Logger.getLogger(SessionManager.class.getName());
    
    // Session attribute keys
    public static final String USER_SESSION_KEY = "user";
    public static final String USER_ID_SESSION_KEY = "userId";
    public static final String USERNAME_SESSION_KEY = "username";
    public static final String USER_ROLE_SESSION_KEY = "userRole";
    public static final String LOGIN_TIME_SESSION_KEY = "loginTime";
    public static final String LAST_ACTIVITY_SESSION_KEY = "lastActivity";
    
    // Session timeout in seconds (30 minutes)
    private static final int SESSION_TIMEOUT = 30 * 60;
    
    /**
     * Create a new session for the user
     * @param request HTTP request
     * @param user the user to create session for
     */
    public static void createUserSession(HttpServletRequest request, User user) {
        if (request == null || user == null) {
            LOGGER.warning("Cannot create session: request or user is null");
            return;
        }
        
        HttpSession session = request.getSession(true);
        session.setMaxInactiveInterval(SESSION_TIMEOUT);
        
        // Store user information in session
        session.setAttribute(USER_SESSION_KEY, user);
        session.setAttribute(USER_ID_SESSION_KEY, user.getUserId());
        session.setAttribute(USERNAME_SESSION_KEY, user.getUsername());
        session.setAttribute(USER_ROLE_SESSION_KEY, user.getRole().toString());
        session.setAttribute(LOGIN_TIME_SESSION_KEY, System.currentTimeMillis());
        session.setAttribute(LAST_ACTIVITY_SESSION_KEY, System.currentTimeMillis());
        
        LOGGER.info("Session created for user: " + user.getUsername());
    }
    
    /**
     * Get the current user from session
     * @param request HTTP request
     * @return User object or null if not logged in
     */
    public static User getCurrentUser(HttpServletRequest request) {
        if (request == null) return null;
        
        HttpSession session = request.getSession(false);
        if (session == null) return null;
        
        // Update last activity
        session.setAttribute(LAST_ACTIVITY_SESSION_KEY, System.currentTimeMillis());
        
        return (User) session.getAttribute(USER_SESSION_KEY);
    }
    
    /**
     * Get the current user ID from session
     * @param request HTTP request
     * @return user ID or -1 if not logged in
     */
    public static int getCurrentUserId(HttpServletRequest request) {
        if (request == null) return -1;
        
        HttpSession session = request.getSession(false);
        if (session == null) return -1;
        
        Integer userId = (Integer) session.getAttribute(USER_ID_SESSION_KEY);
        return userId != null ? userId : -1;
    }
    
    /**
     * Get the current username from session
     * @param request HTTP request
     * @return username or null if not logged in
     */
    public static String getCurrentUsername(HttpServletRequest request) {
        if (request == null) return null;
        
        HttpSession session = request.getSession(false);
        if (session == null) return null;
        
        return (String) session.getAttribute(USERNAME_SESSION_KEY);
    }
    
    /**
     * Get the current user role from session
     * @param request HTTP request
     * @return user role or null if not logged in
     */
    public static String getCurrentUserRole(HttpServletRequest request) {
        if (request == null) return null;
        
        HttpSession session = request.getSession(false);
        if (session == null) return null;
        
        return (String) session.getAttribute(USER_ROLE_SESSION_KEY);
    }
    
    /**
     * Check if user is logged in
     * @param request HTTP request
     * @return true if user is logged in, false otherwise
     */
    public static boolean isLoggedIn(HttpServletRequest request) {
        return getCurrentUser(request) != null;
    }
    
    /**
     * Check if current user is admin
     * @param request HTTP request
     * @return true if user is admin, false otherwise
     */
    public static boolean isAdmin(HttpServletRequest request) {
        String role = getCurrentUserRole(request);
        return "ADMIN".equals(role);
    }
    
    /**
     * Check if current user is regular user
     * @param request HTTP request
     * @return true if user is regular user, false otherwise
     */
    public static boolean isUser(HttpServletRequest request) {
        String role = getCurrentUserRole(request);
        return "USER".equals(role);
    }
    
    /**
     * Invalidate user session (logout)
     * @param request HTTP request
     */
    public static void invalidateSession(HttpServletRequest request) {
        if (request == null) return;
        
        HttpSession session = request.getSession(false);
        if (session != null) {
            String username = getCurrentUsername(request);
            session.invalidate();
            LOGGER.info("Session invalidated for user: " + username);
        }
    }
    
    /**
     * Check if session is expired
     * @param request HTTP request
     * @return true if session is expired, false otherwise
     */
    public static boolean isSessionExpired(HttpServletRequest request) {
        if (request == null) return true;
        
        HttpSession session = request.getSession(false);
        if (session == null) return true;
        
        Long lastActivity = (Long) session.getAttribute(LAST_ACTIVITY_SESSION_KEY);
        if (lastActivity == null) return true;
        
        long currentTime = System.currentTimeMillis();
        long timeSinceLastActivity = (currentTime - lastActivity) / 1000; // Convert to seconds
        
        return timeSinceLastActivity > SESSION_TIMEOUT;
    }
    
    /**
     * Get session duration in minutes
     * @param request HTTP request
     * @return session duration in minutes or -1 if no session
     */
    public static long getSessionDurationMinutes(HttpServletRequest request) {
        if (request == null) return -1;
        
        HttpSession session = request.getSession(false);
        if (session == null) return -1;
        
        Long loginTime = (Long) session.getAttribute(LOGIN_TIME_SESSION_KEY);
        if (loginTime == null) return -1;
        
        long currentTime = System.currentTimeMillis();
        return (currentTime - loginTime) / (1000 * 60); // Convert to minutes
    }
    
    /**
     * Require user to be logged in, redirect to login if not
     * @param request HTTP request
     * @param response HTTP response
     * @param loginUrl URL to redirect to for login
     * @return true if user is logged in, false if redirected
     */
    public static boolean requireLogin(HttpServletRequest request, HttpServletResponse response, String loginUrl) {
        if (!isLoggedIn(request)) {
            try {
                response.sendRedirect(loginUrl);
                return false;
            } catch (Exception e) {
                LOGGER.severe("Failed to redirect to login page: " + e.getMessage());
                return false;
            }
        }
        return true;
    }
    
    /**
     * Require user to be admin, redirect if not
     * @param request HTTP request
     * @param response HTTP response
     * @param unauthorizedUrl URL to redirect to if not authorized
     * @return true if user is admin, false if redirected
     */
    public static boolean requireAdmin(HttpServletRequest request, HttpServletResponse response, String unauthorizedUrl) {
        if (!isAdmin(request)) {
            try {
                response.sendRedirect(unauthorizedUrl);
                return false;
            } catch (Exception e) {
                LOGGER.severe("Failed to redirect to unauthorized page: " + e.getMessage());
                return false;
            }
        }
        return true;
    }
    
    /**
     * Set a session attribute
     * @param request HTTP request
     * @param key attribute key
     * @param value attribute value
     */
    public static void setSessionAttribute(HttpServletRequest request, String key, Object value) {
        if (request == null || key == null) return;
        
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.setAttribute(key, value);
        }
    }
    
    /**
     * Get a session attribute
     * @param request HTTP request
     * @param key attribute key
     * @return attribute value or null
     */
    public static Object getSessionAttribute(HttpServletRequest request, String key) {
        if (request == null || key == null) return null;
        
        HttpSession session = request.getSession(false);
        if (session == null) return null;
        
        return session.getAttribute(key);
    }
    
    /**
     * Remove a session attribute
     * @param request HTTP request
     * @param key attribute key
     */
    public static void removeSessionAttribute(HttpServletRequest request, String key) {
        if (request == null || key == null) return;
        
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.removeAttribute(key);
        }
    }
    
    /**
     * Get session info as string for debugging
     * @param request HTTP request
     * @return session info string
     */
    public static String getSessionInfo(HttpServletRequest request) {
        if (request == null) return "No request";
        
        HttpSession session = request.getSession(false);
        if (session == null) return "No session";
        
        StringBuilder info = new StringBuilder();
        info.append("Session ID: ").append(session.getId()).append("\n");
        info.append("Username: ").append(getCurrentUsername(request)).append("\n");
        info.append("Role: ").append(getCurrentUserRole(request)).append("\n");
        info.append("Duration: ").append(getSessionDurationMinutes(request)).append(" minutes\n");
        info.append("Max Inactive: ").append(session.getMaxInactiveInterval()).append(" seconds\n");
        
        return info.toString();
    }
}
