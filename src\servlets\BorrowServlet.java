package servlets;

import models.Book;
import models.BorrowRecord;
import models.User;
import db.DBConnection;
import utils.SessionManager;
import utils.FineCalculator;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Servlet for handling book borrowing and returning operations
 */
@WebServlet("/borrow/*")
public class BorrowServlet extends HttpServlet {

    private static final Logger LOGGER = Logger.getLogger(BorrowServlet.class.getName());

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (!SessionManager.isLoggedIn(request)) {
            sendErrorResponse(response, 401, "User must be logged in");
            return;
        }

        String pathInfo = request.getPathInfo();
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                handleGetBorrowHistory(request, response);
            } else if (pathInfo.equals("/active")) {
                handleGetActiveBorrows(request, response);
            } else if (pathInfo.equals("/overdue")) {
                handleGetOverdueBorrows(request, response);
            } else if (pathInfo.equals("/fines")) {
                handleGetUserFines(request, response);
            } else {
                sendErrorResponse(response, 404, "Endpoint not found");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BorrowServlet GET", e);
            sendErrorResponse(response, 500, "Internal server error");
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (!SessionManager.isLoggedIn(request)) {
            sendErrorResponse(response, 401, "User must be logged in");
            return;
        }

        String pathInfo = request.getPathInfo();
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                handleBorrowBook(request, response);
            } else if (pathInfo.equals("/return")) {
                handleReturnBook(request, response);
            } else if (pathInfo.equals("/renew")) {
                handleRenewBook(request, response);
            } else if (pathInfo.equals("/pay-fine")) {
                handlePayFine(request, response);
            } else {
                sendErrorResponse(response, 404, "Endpoint not found");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BorrowServlet POST", e);
            sendErrorResponse(response, 500, "Internal server error");
        }
    }

    /**
     * Handle borrow book request
     */
    private void handleBorrowBook(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int userId = SessionManager.getCurrentUserId(request);
        String bookIdParam = request.getParameter("bookId");

        if (bookIdParam == null || bookIdParam.trim().isEmpty()) {
            sendErrorResponse(response, 400, "Book ID is required");
            return;
        }

        try {
            int bookId = Integer.parseInt(bookIdParam);

            // Check if user has unpaid fines
            if (FineCalculator.hasUnpaidFines(userId)) {
                sendErrorResponse(response, 400, "Cannot borrow books with unpaid fines");
                return;
            }

            // Check borrowing limits
            if (hasReachedBorrowingLimit(userId)) {
                sendErrorResponse(response, 400, "Maximum borrowing limit reached");
                return;
            }

            // Check book availability
            Book book = getBookById(bookId);
            if (book == null) {
                sendErrorResponse(response, 404, "Book not found");
                return;
            }

            if (!book.isPhysicallyAvailable()) {
                sendErrorResponse(response, 400, "Book is not available for borrowing");
                return;
            }

            // Check if user already has this book
            if (hasActiveBorrowForBook(userId, bookId)) {
                sendErrorResponse(response, 400, "You already have this book borrowed");
                return;
            }

            // Create borrow record
            if (createBorrowRecord(userId, bookId)) {
                sendSuccessResponse(response, "Book borrowed successfully");
                LOGGER.info("Book borrowed: User " + userId + " borrowed book " + bookId);
            } else {
                sendErrorResponse(response, 500, "Failed to borrow book");
            }

        } catch (NumberFormatException e) {
            sendErrorResponse(response, 400, "Invalid book ID");
        }
    }

    /**
     * Handle return book request
     */
    private void handleReturnBook(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int userId = SessionManager.getCurrentUserId(request);
        String recordIdParam = request.getParameter("recordId");

        if (recordIdParam == null || recordIdParam.trim().isEmpty()) {
            sendErrorResponse(response, 400, "Record ID is required");
            return;
        }

        try {
            int recordId = Integer.parseInt(recordIdParam);

            BorrowRecord record = getBorrowRecordById(recordId);
            if (record == null) {
                sendErrorResponse(response, 404, "Borrow record not found");
                return;
            }

            // Check if record belongs to current user (unless admin)
            if (!SessionManager.isAdmin(request) && record.getUserId() != userId) {
                sendErrorResponse(response, 403, "Access denied");
                return;
            }

            if (record.isReturned()) {
                sendErrorResponse(response, 400, "Book is already returned");
                return;
            }

            // Calculate and apply fine if overdue
            BigDecimal fine = FineCalculator.calculateFine(record);
            if (fine.compareTo(BigDecimal.ZERO) > 0) {
                FineCalculator.createFineRecord(record, fine, "Overdue return");
            }

            // Return the book
            if (returnBook(recordId)) {
                sendSuccessResponse(response, "Book returned successfully" +
                    (fine.compareTo(BigDecimal.ZERO) > 0 ? ". Fine applied: $" + fine : ""));
                LOGGER.info("Book returned: Record " + recordId);
            } else {
                sendErrorResponse(response, 500, "Failed to return book");
            }

        } catch (NumberFormatException e) {
            sendErrorResponse(response, 400, "Invalid record ID");
        }
    }

    /**
     * Handle renew book request
     */
    private void handleRenewBook(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int userId = SessionManager.getCurrentUserId(request);
        String recordIdParam = request.getParameter("recordId");

        if (recordIdParam == null || recordIdParam.trim().isEmpty()) {
            sendErrorResponse(response, 400, "Record ID is required");
            return;
        }

        try {
            int recordId = Integer.parseInt(recordIdParam);

            BorrowRecord record = getBorrowRecordById(recordId);
            if (record == null) {
                sendErrorResponse(response, 404, "Borrow record not found");
                return;
            }

            // Check if record belongs to current user
            if (record.getUserId() != userId) {
                sendErrorResponse(response, 403, "Access denied");
                return;
            }

            if (record.isReturned()) {
                sendErrorResponse(response, 400, "Cannot renew returned book");
                return;
            }

            if (record.isOverdue()) {
                sendErrorResponse(response, 400, "Cannot renew overdue book");
                return;
            }

            // Check if user has unpaid fines
            if (FineCalculator.hasUnpaidFines(userId)) {
                sendErrorResponse(response, 400, "Cannot renew books with unpaid fines");
                return;
            }

            // Renew the book (extend due date by 14 days)
            if (renewBook(recordId)) {
                sendSuccessResponse(response, "Book renewed successfully");
                LOGGER.info("Book renewed: Record " + recordId);
            } else {
                sendErrorResponse(response, 500, "Failed to renew book");
            }

        } catch (NumberFormatException e) {
            sendErrorResponse(response, 400, "Invalid record ID");
        }
    }

    /**
     * Handle pay fine request
     */
    private void handlePayFine(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int userId = SessionManager.getCurrentUserId(request);
        String fineIdParam = request.getParameter("fineId");
        String paymentMethod = request.getParameter("paymentMethod");

        if (fineIdParam == null || fineIdParam.trim().isEmpty()) {
            sendErrorResponse(response, 400, "Fine ID is required");
            return;
        }

        if (paymentMethod == null || paymentMethod.trim().isEmpty()) {
            paymentMethod = "Cash";
        }

        try {
            int fineId = Integer.parseInt(fineIdParam);

            if (payFine(fineId, userId, paymentMethod)) {
                sendSuccessResponse(response, "Fine paid successfully");
                LOGGER.info("Fine paid: Fine " + fineId + " by user " + userId);
            } else {
                sendErrorResponse(response, 500, "Failed to pay fine");
            }

        } catch (NumberFormatException e) {
            sendErrorResponse(response, 400, "Invalid fine ID");
        }
    }

    /**
     * Handle get borrow history
     */
    private void handleGetBorrowHistory(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int userId = SessionManager.getCurrentUserId(request);
        String limitParam = request.getParameter("limit");
        String offsetParam = request.getParameter("offset");

        int limit = 50; // Default limit
        int offset = 0; // Default offset

        try {
            if (limitParam != null) limit = Integer.parseInt(limitParam);
            if (offsetParam != null) offset = Integer.parseInt(offsetParam);
        } catch (NumberFormatException e) {
            sendErrorResponse(response, 400, "Invalid limit or offset parameter");
            return;
        }

        List<BorrowRecord> records = getBorrowHistory(userId, limit, offset);
        sendBorrowRecordsResponse(response, "Borrow history retrieved successfully", records);
    }

    /**
     * Handle get active borrows
     */
    private void handleGetActiveBorrows(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int userId = SessionManager.getCurrentUserId(request);
        List<BorrowRecord> records = getActiveBorrows(userId);
        sendBorrowRecordsResponse(response, "Active borrows retrieved successfully", records);
    }

    /**
     * Handle get overdue borrows
     */
    private void handleGetOverdueBorrows(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int userId = SessionManager.getCurrentUserId(request);
        List<BorrowRecord> records = getOverdueBorrows(userId);
        sendBorrowRecordsResponse(response, "Overdue borrows retrieved successfully", records);
    }

    /**
     * Handle get user fines
     */
    private void handleGetUserFines(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int userId = SessionManager.getCurrentUserId(request);
        BigDecimal totalFines = FineCalculator.getTotalUnpaidFines(userId);

        StringBuilder json = new StringBuilder();
        json.append("{\"success\": true, \"message\": \"Fines retrieved successfully\", ");
        json.append("\"totalUnpaidFines\": ").append(totalFines).append("}");

        response.setStatus(HttpServletResponse.SC_OK);
        PrintWriter out = response.getWriter();
        out.print(json.toString());
        out.flush();
    }

    /**
     * Check if user has reached borrowing limit
     */
    private boolean hasReachedBorrowingLimit(int userId) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();

            // Get max books per user from settings
            String maxBooksQuery = "SELECT setting_value FROM system_settings WHERE setting_key = 'MAX_BOOKS_PER_USER'";
            statement = connection.prepareStatement(maxBooksQuery);
            resultSet = statement.executeQuery();

            int maxBooks = 5; // Default
            if (resultSet.next()) {
                maxBooks = Integer.parseInt(resultSet.getString("setting_value"));
            }

            // Count active borrows
            DBConnection.closeStatement(statement);
            DBConnection.closeResultSet(resultSet);

            String countQuery = "SELECT COUNT(*) FROM borrow_records WHERE user_id = ? AND status IN ('BORROWED', 'OVERDUE')";
            statement = connection.prepareStatement(countQuery);
            statement.setInt(1, userId);
            resultSet = statement.executeQuery();

            if (resultSet.next()) {
                int activeBorrows = resultSet.getInt(1);
                return activeBorrows >= maxBooks;
            }

        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to check borrowing limit", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return false;
    }

    /**
     * Get book by ID
     */
    private Book getBookById(int bookId) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT * FROM books WHERE book_id = ? AND is_active = TRUE";
            statement = connection.prepareStatement(sql);
            statement.setInt(1, bookId);
            resultSet = statement.executeQuery();

            if (resultSet.next()) {
                return mapResultSetToBook(resultSet);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get book by ID", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return null;
    }

    /**
     * Check if user has active borrow for specific book
     */
    private boolean hasActiveBorrowForBook(int userId, int bookId) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT COUNT(*) FROM borrow_records WHERE user_id = ? AND book_id = ? AND status IN ('BORROWED', 'OVERDUE')";
            statement = connection.prepareStatement(sql);
            statement.setInt(1, userId);
            statement.setInt(2, bookId);
            resultSet = statement.executeQuery();

            if (resultSet.next()) {
                return resultSet.getInt(1) > 0;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to check active borrow for book", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return false;
    }

    /**
     * Create borrow record
     */
    private boolean createBorrowRecord(int userId, int bookId) {
        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DBConnection.getConnection(false); // Start transaction

            // Get borrow duration from settings
            String durationQuery = "SELECT setting_value FROM system_settings WHERE setting_key = 'BORROW_DURATION_DAYS'";
            statement = connection.prepareStatement(durationQuery);
            ResultSet resultSet = statement.executeQuery();

            int borrowDays = 14; // Default
            if (resultSet.next()) {
                borrowDays = Integer.parseInt(resultSet.getString("setting_value"));
            }

            DBConnection.closeStatement(statement);
            DBConnection.closeResultSet(resultSet);

            // Calculate due date
            LocalDate dueDate = LocalDate.now().plusDays(borrowDays);

            // Insert borrow record
            String insertQuery = "INSERT INTO borrow_records (user_id, book_id, borrow_date, due_date) VALUES (?, ?, CURDATE(), ?)";
            statement = connection.prepareStatement(insertQuery);
            statement.setInt(1, userId);
            statement.setInt(2, bookId);
            statement.setDate(3, Date.valueOf(dueDate));

            int recordsInserted = statement.executeUpdate();

            if (recordsInserted > 0) {
                // Update book availability
                DBConnection.closeStatement(statement);
                String updateQuery = "UPDATE books SET available_copies = available_copies - 1 WHERE book_id = ? AND available_copies > 0";
                statement = connection.prepareStatement(updateQuery);
                statement.setInt(1, bookId);

                int booksUpdated = statement.executeUpdate();

                if (booksUpdated > 0) {
                    connection.commit();
                    return true;
                } else {
                    connection.rollback();
                    return false;
                }
            } else {
                connection.rollback();
                return false;
            }

        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to create borrow record", e);
            try {
                if (connection != null) connection.rollback();
            } catch (SQLException rollbackEx) {
                LOGGER.log(Level.SEVERE, "Failed to rollback transaction", rollbackEx);
            }
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }

    /**
     * Get borrow record by ID
     */
    private BorrowRecord getBorrowRecordById(int recordId) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();
            String sql = """
                SELECT br.*, b.title, b.author, u.username, u.full_name
                FROM borrow_records br
                JOIN books b ON br.book_id = b.book_id
                JOIN users u ON br.user_id = u.user_id
                WHERE br.record_id = ?
                """;
            statement = connection.prepareStatement(sql);
            statement.setInt(1, recordId);
            resultSet = statement.executeQuery();

            if (resultSet.next()) {
                return mapResultSetToBorrowRecord(resultSet);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get borrow record by ID", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return null;
    }

    /**
     * Return a book
     */
    private boolean returnBook(int recordId) {
        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DBConnection.getConnection(false); // Start transaction

            // Get book ID from record
            String getBookQuery = "SELECT book_id FROM borrow_records WHERE record_id = ?";
            statement = connection.prepareStatement(getBookQuery);
            statement.setInt(1, recordId);
            ResultSet resultSet = statement.executeQuery();

            int bookId = -1;
            if (resultSet.next()) {
                bookId = resultSet.getInt("book_id");
            }

            DBConnection.closeStatement(statement);
            DBConnection.closeResultSet(resultSet);

            if (bookId == -1) {
                connection.rollback();
                return false;
            }

            // Update borrow record
            String updateRecordQuery = "UPDATE borrow_records SET return_date = CURDATE(), status = 'RETURNED' WHERE record_id = ?";
            statement = connection.prepareStatement(updateRecordQuery);
            statement.setInt(1, recordId);

            int recordsUpdated = statement.executeUpdate();

            if (recordsUpdated > 0) {
                // Update book availability
                DBConnection.closeStatement(statement);
                String updateBookQuery = "UPDATE books SET available_copies = available_copies + 1 WHERE book_id = ?";
                statement = connection.prepareStatement(updateBookQuery);
                statement.setInt(1, bookId);

                int booksUpdated = statement.executeUpdate();

                if (booksUpdated > 0) {
                    connection.commit();
                    return true;
                } else {
                    connection.rollback();
                    return false;
                }
            } else {
                connection.rollback();
                return false;
            }

        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to return book", e);
            try {
                if (connection != null) connection.rollback();
            } catch (SQLException rollbackEx) {
                LOGGER.log(Level.SEVERE, "Failed to rollback transaction", rollbackEx);
            }
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }

    /**
     * Renew a book (extend due date)
     */
    private boolean renewBook(int recordId) {
        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DBConnection.getConnection();

            // Get borrow duration from settings
            String durationQuery = "SELECT setting_value FROM system_settings WHERE setting_key = 'BORROW_DURATION_DAYS'";
            statement = connection.prepareStatement(durationQuery);
            ResultSet resultSet = statement.executeQuery();

            int borrowDays = 14; // Default
            if (resultSet.next()) {
                borrowDays = Integer.parseInt(resultSet.getString("setting_value"));
            }

            DBConnection.closeStatement(statement);
            DBConnection.closeResultSet(resultSet);

            // Calculate new due date (extend from current due date)
            String updateQuery = "UPDATE borrow_records SET due_date = DATE_ADD(due_date, INTERVAL ? DAY) WHERE record_id = ?";
            statement = connection.prepareStatement(updateQuery);
            statement.setInt(1, borrowDays);
            statement.setInt(2, recordId);

            return statement.executeUpdate() > 0;

        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to renew book", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }

    /**
     * Pay a fine
     */
    private boolean payFine(int fineId, int userId, String paymentMethod) {
        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DBConnection.getConnection();
            String sql = "UPDATE fines SET is_paid = TRUE, paid_date = CURDATE(), payment_method = ? WHERE fine_id = ? AND user_id = ?";
            statement = connection.prepareStatement(sql);
            statement.setString(1, paymentMethod);
            statement.setInt(2, fineId);
            statement.setInt(3, userId);

            return statement.executeUpdate() > 0;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to pay fine", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }

    /**
     * Get borrow history for user
     */
    private List<BorrowRecord> getBorrowHistory(int userId, int limit, int offset) {
        List<BorrowRecord> records = new ArrayList<>();
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();
            String sql = """
                SELECT br.*, b.title, b.author, u.username, u.full_name
                FROM borrow_records br
                JOIN books b ON br.book_id = b.book_id
                JOIN users u ON br.user_id = u.user_id
                WHERE br.user_id = ?
                ORDER BY br.borrow_date DESC
                LIMIT ? OFFSET ?
                """;
            statement = connection.prepareStatement(sql);
            statement.setInt(1, userId);
            statement.setInt(2, limit);
            statement.setInt(3, offset);
            resultSet = statement.executeQuery();

            while (resultSet.next()) {
                records.add(mapResultSetToBorrowRecord(resultSet));
            }

        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get borrow history", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return records;
    }

    /**
     * Get active borrows for user
     */
    private List<BorrowRecord> getActiveBorrows(int userId) {
        List<BorrowRecord> records = new ArrayList<>();
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();
            String sql = """
                SELECT br.*, b.title, b.author, u.username, u.full_name
                FROM borrow_records br
                JOIN books b ON br.book_id = b.book_id
                JOIN users u ON br.user_id = u.user_id
                WHERE br.user_id = ? AND br.status IN ('BORROWED', 'OVERDUE')
                ORDER BY br.due_date ASC
                """;
            statement = connection.prepareStatement(sql);
            statement.setInt(1, userId);
            resultSet = statement.executeQuery();

            while (resultSet.next()) {
                records.add(mapResultSetToBorrowRecord(resultSet));
            }

        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get active borrows", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return records;
    }

    /**
     * Get overdue borrows for user
     */
    private List<BorrowRecord> getOverdueBorrows(int userId) {
        List<BorrowRecord> records = new ArrayList<>();
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();
            String sql = """
                SELECT br.*, b.title, b.author, u.username, u.full_name
                FROM borrow_records br
                JOIN books b ON br.book_id = b.book_id
                JOIN users u ON br.user_id = u.user_id
                WHERE br.user_id = ? AND br.due_date < CURDATE() AND br.status != 'RETURNED'
                ORDER BY br.due_date ASC
                """;
            statement = connection.prepareStatement(sql);
            statement.setInt(1, userId);
            resultSet = statement.executeQuery();

            while (resultSet.next()) {
                records.add(mapResultSetToBorrowRecord(resultSet));
            }

        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get overdue borrows", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return records;
    }

    /**
     * Map ResultSet to Book object
     */
    private Book mapResultSetToBook(ResultSet resultSet) throws SQLException {
        Book book = new Book();
        book.setBookId(resultSet.getInt("book_id"));
        book.setIsbn(resultSet.getString("isbn"));
        book.setTitle(resultSet.getString("title"));
        book.setAuthor(resultSet.getString("author"));
        book.setGenre(resultSet.getString("genre"));
        book.setPublisher(resultSet.getString("publisher"));
        book.setPublicationYear(resultSet.getInt("publication_year"));
        book.setDescription(resultSet.getString("description"));
        book.setPhysicalCopies(resultSet.getInt("physical_copies"));
        book.setAvailableCopies(resultSet.getInt("available_copies"));
        book.setDigitalAvailable(resultSet.getBoolean("digital_available"));
        book.setDigitalFilePath(resultSet.getString("digital_file_path"));

        String digitalFileType = resultSet.getString("digital_file_type");
        if (digitalFileType != null) {
            book.setDigitalFileType(Book.DigitalFileType.valueOf(digitalFileType));
        }

        book.setCoverImagePath(resultSet.getString("cover_image_path"));
        book.setPrice(resultSet.getBigDecimal("price"));
        book.setActive(resultSet.getBoolean("is_active"));
        book.setCreatedAt(resultSet.getTimestamp("created_at"));
        book.setUpdatedAt(resultSet.getTimestamp("updated_at"));

        return book;
    }

    /**
     * Map ResultSet to BorrowRecord object
     */
    private BorrowRecord mapResultSetToBorrowRecord(ResultSet resultSet) throws SQLException {
        BorrowRecord record = new BorrowRecord();
        record.setRecordId(resultSet.getInt("record_id"));
        record.setUserId(resultSet.getInt("user_id"));
        record.setBookId(resultSet.getInt("book_id"));
        record.setBorrowDate(resultSet.getDate("borrow_date"));
        record.setDueDate(resultSet.getDate("due_date"));
        record.setReturnDate(resultSet.getDate("return_date"));
        record.setStatus(BorrowRecord.Status.valueOf(resultSet.getString("status")));
        record.setFineAmount(resultSet.getBigDecimal("fine_amount"));
        record.setFinePaid(resultSet.getBoolean("fine_paid"));
        record.setCreatedAt(resultSet.getTimestamp("created_at"));
        record.setUpdatedAt(resultSet.getTimestamp("updated_at"));

        // Set related objects if available
        try {
            Book book = new Book();
            book.setTitle(resultSet.getString("title"));
            book.setAuthor(resultSet.getString("author"));
            record.setBook(book);

            User user = new User();
            user.setUsername(resultSet.getString("username"));
            user.setFullName(resultSet.getString("full_name"));
            record.setUser(user);
        } catch (SQLException e) {
            // Related data might not be available in all queries
        }

        return record;
    }

    /**
     * Send success response
     */
    private void sendSuccessResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        PrintWriter out = response.getWriter();
        out.print("{\"success\": true, \"message\": \"" + message + "\"}");
        out.flush();
    }

    /**
     * Send borrow records response
     */
    private void sendBorrowRecordsResponse(HttpServletResponse response, String message, List<BorrowRecord> records) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        PrintWriter out = response.getWriter();

        StringBuilder json = new StringBuilder();
        json.append("{\"success\": true, \"message\": \"").append(message).append("\", \"records\": [");

        for (int i = 0; i < records.size(); i++) {
            json.append(borrowRecordToJson(records.get(i)));
            if (i < records.size() - 1) {
                json.append(",");
            }
        }

        json.append("]}");
        out.print(json.toString());
        out.flush();
    }

    /**
     * Convert borrow record to JSON string
     */
    private String borrowRecordToJson(BorrowRecord record) {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"recordId\": ").append(record.getRecordId()).append(",");
        json.append("\"userId\": ").append(record.getUserId()).append(",");
        json.append("\"bookId\": ").append(record.getBookId()).append(",");
        json.append("\"borrowDate\": \"").append(record.getBorrowDate()).append("\",");
        json.append("\"dueDate\": \"").append(record.getDueDate()).append("\",");
        json.append("\"returnDate\": \"").append(record.getReturnDate() != null ? record.getReturnDate() : "").append("\",");
        json.append("\"status\": \"").append(record.getStatus()).append("\",");
        json.append("\"fineAmount\": ").append(record.getFineAmount() != null ? record.getFineAmount() : "0").append(",");
        json.append("\"finePaid\": ").append(record.isFinePaid()).append(",");
        json.append("\"isOverdue\": ").append(record.isOverdue()).append(",");
        json.append("\"daysOverdue\": ").append(record.getDaysOverdue()).append(",");
        json.append("\"statusDisplay\": \"").append(record.getStatusDisplay()).append("\"");

        if (record.getBook() != null) {
            json.append(",\"book\": {");
            json.append("\"title\": \"").append(record.getBook().getTitle()).append("\",");
            json.append("\"author\": \"").append(record.getBook().getAuthor()).append("\"");
            json.append("}");
        }

        if (record.getUser() != null) {
            json.append(",\"user\": {");
            json.append("\"username\": \"").append(record.getUser().getUsername()).append("\",");
            json.append("\"fullName\": \"").append(record.getUser().getFullName()).append("\"");
            json.append("}");
        }

        json.append("}");
        return json.toString();
    }

    /**
     * Send error response
     */
    private void sendErrorResponse(HttpServletResponse response, int statusCode, String message) throws IOException {
        response.setStatus(statusCode);
        PrintWriter out = response.getWriter();
        out.print("{\"success\": false, \"message\": \"" + message + "\"}");
        out.flush();
    }
}