package db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Central database connection management class for Library Management System
 * Handles JDBC connections, prepared statements, and resource cleanup
 */
public class DBConnection {
    
    private static final Logger LOGGER = Logger.getLogger(DBConnection.class.getName());
    
    // Database configuration
    private static final String DB_URL = "**********************************************";
    private static final String DB_USERNAME = "root";
    private static final String DB_PASSWORD = "Harshalb4u@";
    private static final String DB_DRIVER = "com.mysql.cj.jdbc.Driver";
    
    // Connection pool settings
    private static final int MAX_CONNECTIONS = 20;
    private static final int CONNECTION_TIMEOUT = 30000; // 30 seconds
    
    static {
        try {
            // Load MySQL JDBC driver
            Class.forName(DB_DRIVER);
            LOGGER.info("MySQL JDBC Driver loaded successfully");
        } catch (ClassNotFoundException e) {
            LOGGER.log(Level.SEVERE, "Failed to load MySQL JDBC Driver", e);
            throw new RuntimeException("MySQL JDBC Driver not found", e);
        }
    }
    
    /**
     * Get a database connection
     * @return Connection object
     * @throws SQLException if connection fails
     */
    public static Connection getConnection() throws SQLException {
        try {
            Connection connection = DriverManager.getConnection(DB_URL, DB_USERNAME, DB_PASSWORD);
            connection.setAutoCommit(true); // Enable auto-commit by default
            LOGGER.fine("Database connection established successfully");
            return connection;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to establish database connection", e);
            throw new SQLException("Unable to connect to database: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get a database connection with transaction control
     * @param autoCommit whether to enable auto-commit
     * @return Connection object
     * @throws SQLException if connection fails
     */
    public static Connection getConnection(boolean autoCommit) throws SQLException {
        Connection connection = getConnection();
        connection.setAutoCommit(autoCommit);
        return connection;
    }
    
    /**
     * Close database connection safely
     * @param connection the connection to close
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                if (!connection.isClosed()) {
                    connection.close();
                    LOGGER.fine("Database connection closed successfully");
                }
            } catch (SQLException e) {
                LOGGER.log(Level.WARNING, "Error closing database connection", e);
            }
        }
    }
    
    /**
     * Close PreparedStatement safely
     * @param statement the statement to close
     */
    public static void closeStatement(PreparedStatement statement) {
        if (statement != null) {
            try {
                statement.close();
                LOGGER.fine("PreparedStatement closed successfully");
            } catch (SQLException e) {
                LOGGER.log(Level.WARNING, "Error closing PreparedStatement", e);
            }
        }
    }
    
    /**
     * Close Statement safely
     * @param statement the statement to close
     */
    public static void closeStatement(Statement statement) {
        if (statement != null) {
            try {
                statement.close();
                LOGGER.fine("Statement closed successfully");
            } catch (SQLException e) {
                LOGGER.log(Level.WARNING, "Error closing Statement", e);
            }
        }
    }
    
    /**
     * Close ResultSet safely
     * @param resultSet the result set to close
     */
    public static void closeResultSet(ResultSet resultSet) {
        if (resultSet != null) {
            try {
                resultSet.close();
                LOGGER.fine("ResultSet closed successfully");
            } catch (SQLException e) {
                LOGGER.log(Level.WARNING, "Error closing ResultSet", e);
            }
        }
    }
    
    /**
     * Close all resources safely
     * @param connection the connection to close
     * @param statement the statement to close
     * @param resultSet the result set to close
     */
    public static void closeResources(Connection connection, PreparedStatement statement, ResultSet resultSet) {
        closeResultSet(resultSet);
        closeStatement(statement);
        closeConnection(connection);
    }
    
    /**
     * Close all resources safely
     * @param connection the connection to close
     * @param statement the statement to close
     */
    public static void closeResources(Connection connection, PreparedStatement statement) {
        closeStatement(statement);
        closeConnection(connection);
    }
    
    /**
     * Execute a transaction with rollback support
     * @param transaction the transaction to execute
     * @return true if transaction succeeded, false otherwise
     */
    public static boolean executeTransaction(DatabaseTransaction transaction) {
        Connection connection = null;
        try {
            connection = getConnection(false); // Disable auto-commit for transaction
            boolean result = transaction.execute(connection);
            if (result) {
                connection.commit();
                LOGGER.info("Transaction committed successfully");
            } else {
                connection.rollback();
                LOGGER.warning("Transaction rolled back due to failure");
            }
            return result;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Transaction failed", e);
            if (connection != null) {
                try {
                    connection.rollback();
                    LOGGER.info("Transaction rolled back due to exception");
                } catch (SQLException rollbackEx) {
                    LOGGER.log(Level.SEVERE, "Failed to rollback transaction", rollbackEx);
                }
            }
            return false;
        } finally {
            closeConnection(connection);
        }
    }
    
    /**
     * Test database connectivity
     * @return true if connection is successful, false otherwise
     */
    public static boolean testConnection() {
        Connection connection = null;
        try {
            connection = getConnection();
            boolean isValid = connection.isValid(CONNECTION_TIMEOUT / 1000);
            if (isValid) {
                LOGGER.info("Database connection test successful");
            } else {
                LOGGER.warning("Database connection test failed");
            }
            return isValid;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Database connection test failed", e);
            return false;
        } finally {
            closeConnection(connection);
        }
    }
    
    /**
     * Get database metadata information
     * @return String containing database info
     */
    public static String getDatabaseInfo() {
        Connection connection = null;
        try {
            connection = getConnection();
            return String.format("Database: %s, Driver: %s, Version: %s",
                connection.getMetaData().getDatabaseProductName(),
                connection.getMetaData().getDriverName(),
                connection.getMetaData().getDatabaseProductVersion());
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Failed to get database info", e);
            return "Database info unavailable";
        } finally {
            closeConnection(connection);
        }
    }
    
    /**
     * Interface for database transactions
     */
    @FunctionalInterface
    public interface DatabaseTransaction {
        boolean execute(Connection connection) throws SQLException;
    }
}
