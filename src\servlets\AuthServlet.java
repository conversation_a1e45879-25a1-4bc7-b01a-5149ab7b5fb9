package servlets;

import models.User;
import db.DBConnection;
import utils.SessionManager;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Servlet for handling user authentication (login, logout, registration)
 */
@WebServlet("/auth/*")
public class AuthServlet extends HttpServlet {
    
    private static final Logger LOGGER = Logger.getLogger(AuthServlet.class.getName());
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        try {
            switch (pathInfo) {
                case "/status":
                    handleGetStatus(request, response);
                    break;
                case "/logout":
                    handleLogout(request, response);
                    break;
                case "/profile":
                    handleGetProfile(request, response);
                    break;
                default:
                    sendErrorResponse(response, 404, "Endpoint not found");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in AuthServlet GET", e);
            sendErrorResponse(response, 500, "Internal server error");
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        try {
            switch (pathInfo) {
                case "/login":
                    handleLogin(request, response);
                    break;
                case "/register":
                    handleRegister(request, response);
                    break;
                case "/change-password":
                    handleChangePassword(request, response);
                    break;
                default:
                    sendErrorResponse(response, 404, "Endpoint not found");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in AuthServlet POST", e);
            sendErrorResponse(response, 500, "Internal server error");
        }
    }
    
    /**
     * Handle user login
     */
    private void handleLogin(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        
        if (username == null || password == null || username.trim().isEmpty() || password.trim().isEmpty()) {
            sendErrorResponse(response, 400, "Username and password are required");
            return;
        }
        
        User user = authenticateUser(username.trim(), password);
        if (user != null) {
            SessionManager.createUserSession(request, user);
            sendSuccessResponse(response, "Login successful", user);
            LOGGER.info("User logged in: " + username);
        } else {
            sendErrorResponse(response, 401, "Invalid username or password");
        }
    }
    
    /**
     * Handle user registration
     */
    private void handleRegister(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        String email = request.getParameter("email");
        String fullName = request.getParameter("fullName");
        String phone = request.getParameter("phone");
        String address = request.getParameter("address");
        
        // Validate required fields
        if (username == null || password == null || email == null || fullName == null ||
            username.trim().isEmpty() || password.trim().isEmpty() || 
            email.trim().isEmpty() || fullName.trim().isEmpty()) {
            sendErrorResponse(response, 400, "Username, password, email, and full name are required");
            return;
        }
        
        // Validate password length
        if (password.length() < 6) {
            sendErrorResponse(response, 400, "Password must be at least 6 characters long");
            return;
        }
        
        // Validate email format
        if (!email.contains("@")) {
            sendErrorResponse(response, 400, "Invalid email format");
            return;
        }
        
        // Check if username or email already exists
        if (isUsernameExists(username) || isEmailExists(email)) {
            sendErrorResponse(response, 409, "Username or email already exists");
            return;
        }
        
        // Create new user
        User newUser = new User(username.trim(), password, email.trim(), fullName.trim());
        newUser.setPhone(phone);
        newUser.setAddress(address);
        
        if (createUser(newUser)) {
            // Auto-login after registration
            User createdUser = authenticateUser(username, password);
            if (createdUser != null) {
                SessionManager.createUserSession(request, createdUser);
                sendSuccessResponse(response, "Registration successful", createdUser);
                LOGGER.info("New user registered and logged in: " + username);
            } else {
                sendSuccessResponse(response, "Registration successful, please login", null);
            }
        } else {
            sendErrorResponse(response, 500, "Failed to create user account");
        }
    }
    
    /**
     * Handle user logout
     */
    private void handleLogout(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        String username = SessionManager.getCurrentUsername(request);
        SessionManager.invalidateSession(request);
        sendSuccessResponse(response, "Logout successful", null);
        LOGGER.info("User logged out: " + username);
    }
    
    /**
     * Handle get login status
     */
    private void handleGetStatus(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        User currentUser = SessionManager.getCurrentUser(request);
        if (currentUser != null) {
            sendSuccessResponse(response, "User is logged in", currentUser);
        } else {
            sendErrorResponse(response, 401, "User is not logged in");
        }
    }
    
    /**
     * Handle get user profile
     */
    private void handleGetProfile(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        User currentUser = SessionManager.getCurrentUser(request);
        if (currentUser == null) {
            sendErrorResponse(response, 401, "User is not logged in");
            return;
        }
        
        // Get fresh user data from database
        User freshUser = getUserById(currentUser.getUserId());
        if (freshUser != null) {
            sendSuccessResponse(response, "Profile retrieved successfully", freshUser);
        } else {
            sendErrorResponse(response, 404, "User profile not found");
        }
    }
    
    /**
     * Handle change password
     */
    private void handleChangePassword(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        User currentUser = SessionManager.getCurrentUser(request);
        if (currentUser == null) {
            sendErrorResponse(response, 401, "User is not logged in");
            return;
        }
        
        String currentPassword = request.getParameter("currentPassword");
        String newPassword = request.getParameter("newPassword");
        
        if (currentPassword == null || newPassword == null || 
            currentPassword.trim().isEmpty() || newPassword.trim().isEmpty()) {
            sendErrorResponse(response, 400, "Current password and new password are required");
            return;
        }
        
        if (newPassword.length() < 6) {
            sendErrorResponse(response, 400, "New password must be at least 6 characters long");
            return;
        }
        
        // Verify current password
        User verifiedUser = authenticateUser(currentUser.getUsername(), currentPassword);
        if (verifiedUser == null) {
            sendErrorResponse(response, 401, "Current password is incorrect");
            return;
        }
        
        // Update password
        if (updateUserPassword(currentUser.getUserId(), newPassword)) {
            sendSuccessResponse(response, "Password changed successfully", null);
            LOGGER.info("Password changed for user: " + currentUser.getUsername());
        } else {
            sendErrorResponse(response, 500, "Failed to change password");
        }
    }
    
    /**
     * Authenticate user with username and password
     */
    private User authenticateUser(String username, String password) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT * FROM users WHERE username = ? AND password = ? AND is_active = TRUE";
            statement = connection.prepareStatement(sql);
            statement.setString(1, username);
            statement.setString(2, password); // In production, use hashed passwords
            resultSet = statement.executeQuery();
            
            if (resultSet.next()) {
                return mapResultSetToUser(resultSet);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to authenticate user", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }
        
        return null;
    }
    
    /**
     * Create a new user in the database
     */
    private boolean createUser(User user) {
        Connection connection = null;
        PreparedStatement statement = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "INSERT INTO users (username, password, email, full_name, phone, address, role) VALUES (?, ?, ?, ?, ?, ?, ?)";
            statement = connection.prepareStatement(sql);
            statement.setString(1, user.getUsername());
            statement.setString(2, user.getPassword()); // In production, hash the password
            statement.setString(3, user.getEmail());
            statement.setString(4, user.getFullName());
            statement.setString(5, user.getPhone());
            statement.setString(6, user.getAddress());
            statement.setString(7, user.getRole().toString());
            
            return statement.executeUpdate() > 0;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to create user", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }
    
    /**
     * Check if username exists
     */
    private boolean isUsernameExists(String username) {
        return checkFieldExists("username", username);
    }
    
    /**
     * Check if email exists
     */
    private boolean isEmailExists(String email) {
        return checkFieldExists("email", email);
    }
    
    /**
     * Check if a field value exists in users table
     */
    private boolean checkFieldExists(String fieldName, String value) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT COUNT(*) FROM users WHERE " + fieldName + " = ?";
            statement = connection.prepareStatement(sql);
            statement.setString(1, value);
            resultSet = statement.executeQuery();
            
            if (resultSet.next()) {
                return resultSet.getInt(1) > 0;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to check field existence", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }
        
        return false;
    }
    
    /**
     * Get user by ID
     */
    private User getUserById(int userId) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT * FROM users WHERE user_id = ? AND is_active = TRUE";
            statement = connection.prepareStatement(sql);
            statement.setInt(1, userId);
            resultSet = statement.executeQuery();
            
            if (resultSet.next()) {
                return mapResultSetToUser(resultSet);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get user by ID", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }
        
        return null;
    }
    
    /**
     * Update user password
     */
    private boolean updateUserPassword(int userId, String newPassword) {
        Connection connection = null;
        PreparedStatement statement = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "UPDATE users SET password = ? WHERE user_id = ?";
            statement = connection.prepareStatement(sql);
            statement.setString(1, newPassword); // In production, hash the password
            statement.setInt(2, userId);
            
            return statement.executeUpdate() > 0;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to update user password", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }
    
    /**
     * Map ResultSet to User object
     */
    private User mapResultSetToUser(ResultSet resultSet) throws SQLException {
        User user = new User();
        user.setUserId(resultSet.getInt("user_id"));
        user.setUsername(resultSet.getString("username"));
        user.setPassword(resultSet.getString("password"));
        user.setEmail(resultSet.getString("email"));
        user.setFullName(resultSet.getString("full_name"));
        user.setRole(User.Role.valueOf(resultSet.getString("role")));
        user.setPhone(resultSet.getString("phone"));
        user.setAddress(resultSet.getString("address"));
        user.setMembershipDate(resultSet.getDate("membership_date"));
        user.setActive(resultSet.getBoolean("is_active"));
        user.setCreatedAt(resultSet.getTimestamp("created_at"));
        user.setUpdatedAt(resultSet.getTimestamp("updated_at"));
        return user;
    }
    
    /**
     * Send success response
     */
    private void sendSuccessResponse(HttpServletResponse response, String message, Object data) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        PrintWriter out = response.getWriter();
        
        StringBuilder json = new StringBuilder();
        json.append("{\"success\": true, \"message\": \"").append(message).append("\"");
        
        if (data != null && data instanceof User) {
            User user = (User) data;
            json.append(", \"user\": {");
            json.append("\"userId\": ").append(user.getUserId()).append(",");
            json.append("\"username\": \"").append(user.getUsername()).append("\",");
            json.append("\"email\": \"").append(user.getEmail()).append("\",");
            json.append("\"fullName\": \"").append(user.getFullName()).append("\",");
            json.append("\"role\": \"").append(user.getRole()).append("\",");
            json.append("\"isActive\": ").append(user.isActive());
            json.append("}");
        }
        
        json.append("}");
        out.print(json.toString());
        out.flush();
    }
    
    /**
     * Send error response
     */
    private void sendErrorResponse(HttpServletResponse response, int statusCode, String message) throws IOException {
        response.setStatus(statusCode);
        PrintWriter out = response.getWriter();
        out.print("{\"success\": false, \"message\": \"" + message + "\"}");
        out.flush();
    }
}
