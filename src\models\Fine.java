package models;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * Fine model class representing fines imposed on users for overdue books
 */
public class Fine {
    
    private int fineId;
    private int userId;
    private int recordId;
    private BigDecimal fineAmount;
    private String fineReason;
    private Date fineDate;
    private Date paidDate;
    private boolean isPaid;
    private String paymentMethod;
    private Timestamp createdAt;
    
    // Related objects (for joins)
    private User user;
    private BorrowRecord borrowRecord;
    
    // Default constructor
    public Fine() {
        this.fineDate = new Date(System.currentTimeMillis());
        this.isPaid = false;
        this.fineAmount = BigDecimal.ZERO;
    }
    
    // Constructor with essential fields
    public Fine(int userId, int recordId, BigDecimal fineAmount, String fineReason) {
        this();
        this.userId = userId;
        this.recordId = recordId;
        this.fineAmount = fineAmount;
        this.fineReason = fineReason;
    }
    
    // Constructor with objects
    public Fine(User user, BorrowRecord borrowRecord, BigDecimal fineAmount, String fineReason) {
        this(user.getUserId(), borrowRecord.getRecordId(), fineAmount, fineReason);
        this.user = user;
        this.borrowRecord = borrowRecord;
    }
    
    // Full constructor
    public Fine(int fineId, int userId, int recordId, BigDecimal fineAmount, String fineReason,
               Date fineDate, Date paidDate, boolean isPaid, String paymentMethod, Timestamp createdAt) {
        this.fineId = fineId;
        this.userId = userId;
        this.recordId = recordId;
        this.fineAmount = fineAmount;
        this.fineReason = fineReason;
        this.fineDate = fineDate;
        this.paidDate = paidDate;
        this.isPaid = isPaid;
        this.paymentMethod = paymentMethod;
        this.createdAt = createdAt;
    }
    
    // Getters and Setters
    public int getFineId() {
        return fineId;
    }
    
    public void setFineId(int fineId) {
        this.fineId = fineId;
    }
    
    public int getUserId() {
        return userId;
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
    }
    
    public int getRecordId() {
        return recordId;
    }
    
    public void setRecordId(int recordId) {
        this.recordId = recordId;
    }
    
    public BigDecimal getFineAmount() {
        return fineAmount;
    }
    
    public void setFineAmount(BigDecimal fineAmount) {
        this.fineAmount = fineAmount;
    }
    
    public String getFineReason() {
        return fineReason;
    }
    
    public void setFineReason(String fineReason) {
        this.fineReason = fineReason;
    }
    
    public Date getFineDate() {
        return fineDate;
    }
    
    public void setFineDate(Date fineDate) {
        this.fineDate = fineDate;
    }
    
    public Date getPaidDate() {
        return paidDate;
    }
    
    public void setPaidDate(Date paidDate) {
        this.paidDate = paidDate;
    }
    
    public boolean isPaid() {
        return isPaid;
    }
    
    public void setPaid(boolean paid) {
        isPaid = paid;
    }
    
    public String getPaymentMethod() {
        return paymentMethod;
    }
    
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    
    public Timestamp getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public BorrowRecord getBorrowRecord() {
        return borrowRecord;
    }
    
    public void setBorrowRecord(BorrowRecord borrowRecord) {
        this.borrowRecord = borrowRecord;
    }
    
    // Utility methods
    public boolean isOverdue() {
        return !isPaid;
    }
    
    public String getFormattedAmount() {
        return fineAmount != null ? "$" + fineAmount.toString() : "$0.00";
    }
    
    public String getStatusDisplay() {
        return isPaid ? "Paid" : "Unpaid";
    }
    
    /**
     * Mark fine as paid
     * @param paymentMethod the method used for payment
     */
    public void markAsPaid(String paymentMethod) {
        this.isPaid = true;
        this.paidDate = new Date(System.currentTimeMillis());
        this.paymentMethod = paymentMethod;
    }
    
    /**
     * Get days since fine was imposed
     * @return number of days
     */
    public long getDaysSinceFineImposed() {
        long diffInMillies = System.currentTimeMillis() - fineDate.getTime();
        return diffInMillies / (1000 * 60 * 60 * 24);
    }
    
    /**
     * Check if fine is recent (within last 7 days)
     * @return true if recent, false otherwise
     */
    public boolean isRecent() {
        return getDaysSinceFineImposed() <= 7;
    }
    
    /**
     * Get payment delay in days (if paid)
     * @return number of days between fine date and payment date
     */
    public long getPaymentDelayDays() {
        if (!isPaid || paidDate == null) return 0;
        long diffInMillies = paidDate.getTime() - fineDate.getTime();
        return diffInMillies / (1000 * 60 * 60 * 24);
    }
    
    @Override
    public String toString() {
        return "Fine{" +
                "fineId=" + fineId +
                ", userId=" + userId +
                ", recordId=" + recordId +
                ", fineAmount=" + fineAmount +
                ", fineReason='" + fineReason + '\'' +
                ", fineDate=" + fineDate +
                ", paidDate=" + paidDate +
                ", isPaid=" + isPaid +
                ", paymentMethod='" + paymentMethod + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Fine fine = (Fine) obj;
        return fineId == fine.fineId;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(fineId);
    }
}
