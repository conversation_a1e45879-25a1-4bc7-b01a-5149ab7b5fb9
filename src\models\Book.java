package models;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Book model class representing both physical and digital books in the library
 */
public class Book {
    
    public enum DigitalFileType {
        PDF, EPUB, MOBI
    }
    
    private int bookId;
    private String isbn;
    private String title;
    private String author;
    private String genre;
    private String publisher;
    private int publicationYear;
    private String description;
    private int physicalCopies;
    private int availableCopies;
    private boolean digitalAvailable;
    private String digitalFilePath;
    private DigitalFileType digitalFileType;
    private String coverImagePath;
    private BigDecimal price;
    private boolean isActive;
    private Timestamp createdAt;
    private Timestamp updatedAt;
    
    // Default constructor
    public Book() {
        this.isActive = true;
        this.physicalCopies = 0;
        this.availableCopies = 0;
        this.digitalAvailable = false;
        this.price = BigDecimal.ZERO;
    }
    
    // Constructor with essential fields
    public Book(String title, String author, String genre) {
        this();
        this.title = title;
        this.author = author;
        this.genre = genre;
    }
    
    // Constructor with more details
    public Book(String isbn, String title, String author, String genre, String publisher, 
                int publicationYear, String description, int physicalCopies) {
        this(title, author, genre);
        this.isbn = isbn;
        this.publisher = publisher;
        this.publicationYear = publicationYear;
        this.description = description;
        this.physicalCopies = physicalCopies;
        this.availableCopies = physicalCopies;
    }
    
    // Full constructor
    public Book(int bookId, String isbn, String title, String author, String genre, String publisher,
                int publicationYear, String description, int physicalCopies, int availableCopies,
                boolean digitalAvailable, String digitalFilePath, DigitalFileType digitalFileType,
                String coverImagePath, BigDecimal price, boolean isActive, Timestamp createdAt, Timestamp updatedAt) {
        this.bookId = bookId;
        this.isbn = isbn;
        this.title = title;
        this.author = author;
        this.genre = genre;
        this.publisher = publisher;
        this.publicationYear = publicationYear;
        this.description = description;
        this.physicalCopies = physicalCopies;
        this.availableCopies = availableCopies;
        this.digitalAvailable = digitalAvailable;
        this.digitalFilePath = digitalFilePath;
        this.digitalFileType = digitalFileType;
        this.coverImagePath = coverImagePath;
        this.price = price;
        this.isActive = isActive;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Getters and Setters
    public int getBookId() {
        return bookId;
    }
    
    public void setBookId(int bookId) {
        this.bookId = bookId;
    }
    
    public String getIsbn() {
        return isbn;
    }
    
    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getAuthor() {
        return author;
    }
    
    public void setAuthor(String author) {
        this.author = author;
    }
    
    public String getGenre() {
        return genre;
    }
    
    public void setGenre(String genre) {
        this.genre = genre;
    }
    
    public String getPublisher() {
        return publisher;
    }
    
    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }
    
    public int getPublicationYear() {
        return publicationYear;
    }
    
    public void setPublicationYear(int publicationYear) {
        this.publicationYear = publicationYear;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public int getPhysicalCopies() {
        return physicalCopies;
    }
    
    public void setPhysicalCopies(int physicalCopies) {
        this.physicalCopies = physicalCopies;
    }
    
    public int getAvailableCopies() {
        return availableCopies;
    }
    
    public void setAvailableCopies(int availableCopies) {
        this.availableCopies = availableCopies;
    }
    
    public boolean isDigitalAvailable() {
        return digitalAvailable;
    }
    
    public void setDigitalAvailable(boolean digitalAvailable) {
        this.digitalAvailable = digitalAvailable;
    }
    
    public String getDigitalFilePath() {
        return digitalFilePath;
    }
    
    public void setDigitalFilePath(String digitalFilePath) {
        this.digitalFilePath = digitalFilePath;
    }
    
    public DigitalFileType getDigitalFileType() {
        return digitalFileType;
    }
    
    public void setDigitalFileType(DigitalFileType digitalFileType) {
        this.digitalFileType = digitalFileType;
    }
    
    public String getCoverImagePath() {
        return coverImagePath;
    }
    
    public void setCoverImagePath(String coverImagePath) {
        this.coverImagePath = coverImagePath;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public Timestamp getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }
    
    public Timestamp getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Utility methods
    public boolean isPhysicallyAvailable() {
        return availableCopies > 0;
    }
    
    public boolean isBorrowable() {
        return isActive && (isPhysicallyAvailable() || digitalAvailable);
    }
    
    public void borrowPhysicalCopy() {
        if (availableCopies > 0) {
            availableCopies--;
        }
    }
    
    public void returnPhysicalCopy() {
        if (availableCopies < physicalCopies) {
            availableCopies++;
        }
    }
    
    public String getAvailabilityStatus() {
        if (!isActive) return "Inactive";
        if (availableCopies > 0 && digitalAvailable) return "Available (Physical & Digital)";
        if (availableCopies > 0) return "Available (Physical)";
        if (digitalAvailable) return "Available (Digital Only)";
        return "Not Available";
    }
    
    public String getFormattedPrice() {
        return price != null ? "$" + price.toString() : "N/A";
    }
    
    @Override
    public String toString() {
        return "Book{" +
                "bookId=" + bookId +
                ", isbn='" + isbn + '\'' +
                ", title='" + title + '\'' +
                ", author='" + author + '\'' +
                ", genre='" + genre + '\'' +
                ", availableCopies=" + availableCopies +
                ", digitalAvailable=" + digitalAvailable +
                ", isActive=" + isActive +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Book book = (Book) obj;
        return bookId == book.bookId;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(bookId);
    }
}
