package servlets;

import models.Book;
import db.DBConnection;
import utils.SessionManager;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Servlet for handling book management operations (CRUD)
 */
@WebServlet("/books/*")
public class BookServlet extends HttpServlet {

    private static final Logger LOGGER = Logger.getLogger(BookServlet.class.getName());

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                handleGetAllBooks(request, response);
            } else if (pathInfo.startsWith("/")) {
                String[] pathParts = pathInfo.split("/");
                if (pathParts.length == 2) {
                    try {
                        int bookId = Integer.parseInt(pathParts[1]);
                        handleGetBookById(request, response, bookId);
                    } catch (NumberFormatException e) {
                        sendErrorResponse(response, 400, "Invalid book ID");
                    }
                } else {
                    sendErrorResponse(response, 404, "Endpoint not found");
                }
            } else {
                sendErrorResponse(response, 404, "Endpoint not found");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BookServlet GET", e);
            sendErrorResponse(response, 500, "Internal server error");
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Only admins can add books
        if (!SessionManager.isAdmin(request)) {
            sendErrorResponse(response, 403, "Admin access required");
            return;
        }

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            handleAddBook(request, response);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BookServlet POST", e);
            sendErrorResponse(response, 500, "Internal server error");
        }
    }

    @Override
    protected void doPut(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Only admins can update books
        if (!SessionManager.isAdmin(request)) {
            sendErrorResponse(response, 403, "Admin access required");
            return;
        }

        String pathInfo = request.getPathInfo();
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            if (pathInfo != null && pathInfo.startsWith("/")) {
                String[] pathParts = pathInfo.split("/");
                if (pathParts.length == 2) {
                    try {
                        int bookId = Integer.parseInt(pathParts[1]);
                        handleUpdateBook(request, response, bookId);
                    } catch (NumberFormatException e) {
                        sendErrorResponse(response, 400, "Invalid book ID");
                    }
                } else {
                    sendErrorResponse(response, 404, "Endpoint not found");
                }
            } else {
                sendErrorResponse(response, 404, "Endpoint not found");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BookServlet PUT", e);
            sendErrorResponse(response, 500, "Internal server error");
        }
    }

    @Override
    protected void doDelete(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Only admins can delete books
        if (!SessionManager.isAdmin(request)) {
            sendErrorResponse(response, 403, "Admin access required");
            return;
        }

        String pathInfo = request.getPathInfo();
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            if (pathInfo != null && pathInfo.startsWith("/")) {
                String[] pathParts = pathInfo.split("/");
                if (pathParts.length == 2) {
                    try {
                        int bookId = Integer.parseInt(pathParts[1]);
                        handleDeleteBook(request, response, bookId);
                    } catch (NumberFormatException e) {
                        sendErrorResponse(response, 400, "Invalid book ID");
                    }
                } else {
                    sendErrorResponse(response, 404, "Endpoint not found");
                }
            } else {
                sendErrorResponse(response, 404, "Endpoint not found");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BookServlet DELETE", e);
            sendErrorResponse(response, 500, "Internal server error");
        }
    }

    /**
     * Handle get all books with optional filtering
     */
    private void handleGetAllBooks(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        String genre = request.getParameter("genre");
        String author = request.getParameter("author");
        String availableOnly = request.getParameter("availableOnly");
        String digitalOnly = request.getParameter("digitalOnly");
        String limitParam = request.getParameter("limit");
        String offsetParam = request.getParameter("offset");

        int limit = 50; // Default limit
        int offset = 0; // Default offset

        try {
            if (limitParam != null) limit = Integer.parseInt(limitParam);
            if (offsetParam != null) offset = Integer.parseInt(offsetParam);
        } catch (NumberFormatException e) {
            sendErrorResponse(response, 400, "Invalid limit or offset parameter");
            return;
        }

        List<Book> books = getAllBooks(genre, author, availableOnly, digitalOnly, limit, offset);
        sendBooksResponse(response, "Books retrieved successfully", books);
    }

    /**
     * Handle get book by ID
     */
    private void handleGetBookById(HttpServletRequest request, HttpServletResponse response, int bookId)
            throws IOException {

        Book book = getBookById(bookId);
        if (book != null) {
            sendBookResponse(response, "Book retrieved successfully", book);
        } else {
            sendErrorResponse(response, 404, "Book not found");
        }
    }

    /**
     * Handle add new book
     */
    private void handleAddBook(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        // Extract book data from request
        String isbn = request.getParameter("isbn");
        String title = request.getParameter("title");
        String author = request.getParameter("author");
        String genre = request.getParameter("genre");
        String publisher = request.getParameter("publisher");
        String publicationYearParam = request.getParameter("publicationYear");
        String description = request.getParameter("description");
        String physicalCopiesParam = request.getParameter("physicalCopies");
        String digitalAvailableParam = request.getParameter("digitalAvailable");
        String digitalFilePath = request.getParameter("digitalFilePath");
        String digitalFileType = request.getParameter("digitalFileType");
        String coverImagePath = request.getParameter("coverImagePath");
        String priceParam = request.getParameter("price");

        // Validate required fields
        if (title == null || author == null || title.trim().isEmpty() || author.trim().isEmpty()) {
            sendErrorResponse(response, 400, "Title and author are required");
            return;
        }

        try {
            Book book = new Book();
            book.setIsbn(isbn);
            book.setTitle(title.trim());
            book.setAuthor(author.trim());
            book.setGenre(genre);
            book.setPublisher(publisher);

            if (publicationYearParam != null && !publicationYearParam.trim().isEmpty()) {
                book.setPublicationYear(Integer.parseInt(publicationYearParam));
            }

            book.setDescription(description);

            if (physicalCopiesParam != null && !physicalCopiesParam.trim().isEmpty()) {
                int physicalCopies = Integer.parseInt(physicalCopiesParam);
                book.setPhysicalCopies(physicalCopies);
                book.setAvailableCopies(physicalCopies);
            }

            if (digitalAvailableParam != null) {
                book.setDigitalAvailable(Boolean.parseBoolean(digitalAvailableParam));
            }

            book.setDigitalFilePath(digitalFilePath);

            if (digitalFileType != null && !digitalFileType.trim().isEmpty()) {
                book.setDigitalFileType(Book.DigitalFileType.valueOf(digitalFileType.toUpperCase()));
            }

            book.setCoverImagePath(coverImagePath);

            if (priceParam != null && !priceParam.trim().isEmpty()) {
                book.setPrice(new BigDecimal(priceParam));
            }

            if (createBook(book)) {
                sendSuccessResponse(response, "Book added successfully");
                LOGGER.info("Book added: " + title + " by " + author);
            } else {
                sendErrorResponse(response, 500, "Failed to add book");
            }

        } catch (NumberFormatException e) {
            sendErrorResponse(response, 400, "Invalid number format in request parameters");
        } catch (IllegalArgumentException e) {
            sendErrorResponse(response, 400, "Invalid digital file type");
        }
    }

    /**
     * Handle update book
     */
    private void handleUpdateBook(HttpServletRequest request, HttpServletResponse response, int bookId)
            throws IOException {

        Book existingBook = getBookById(bookId);
        if (existingBook == null) {
            sendErrorResponse(response, 404, "Book not found");
            return;
        }

        // Extract updated book data from request
        String isbn = request.getParameter("isbn");
        String title = request.getParameter("title");
        String author = request.getParameter("author");
        String genre = request.getParameter("genre");
        String publisher = request.getParameter("publisher");
        String publicationYearParam = request.getParameter("publicationYear");
        String description = request.getParameter("description");
        String physicalCopiesParam = request.getParameter("physicalCopies");
        String digitalAvailableParam = request.getParameter("digitalAvailable");
        String digitalFilePath = request.getParameter("digitalFilePath");
        String digitalFileType = request.getParameter("digitalFileType");
        String coverImagePath = request.getParameter("coverImagePath");
        String priceParam = request.getParameter("price");
        String isActiveParam = request.getParameter("isActive");

        try {
            // Update only provided fields
            if (isbn != null) existingBook.setIsbn(isbn);
            if (title != null && !title.trim().isEmpty()) existingBook.setTitle(title.trim());
            if (author != null && !author.trim().isEmpty()) existingBook.setAuthor(author.trim());
            if (genre != null) existingBook.setGenre(genre);
            if (publisher != null) existingBook.setPublisher(publisher);

            if (publicationYearParam != null && !publicationYearParam.trim().isEmpty()) {
                existingBook.setPublicationYear(Integer.parseInt(publicationYearParam));
            }

            if (description != null) existingBook.setDescription(description);

            if (physicalCopiesParam != null && !physicalCopiesParam.trim().isEmpty()) {
                int newPhysicalCopies = Integer.parseInt(physicalCopiesParam);
                int currentBorrowed = existingBook.getPhysicalCopies() - existingBook.getAvailableCopies();
                existingBook.setPhysicalCopies(newPhysicalCopies);
                existingBook.setAvailableCopies(Math.max(0, newPhysicalCopies - currentBorrowed));
            }

            if (digitalAvailableParam != null) {
                existingBook.setDigitalAvailable(Boolean.parseBoolean(digitalAvailableParam));
            }

            if (digitalFilePath != null) existingBook.setDigitalFilePath(digitalFilePath);

            if (digitalFileType != null && !digitalFileType.trim().isEmpty()) {
                existingBook.setDigitalFileType(Book.DigitalFileType.valueOf(digitalFileType.toUpperCase()));
            }

            if (coverImagePath != null) existingBook.setCoverImagePath(coverImagePath);

            if (priceParam != null && !priceParam.trim().isEmpty()) {
                existingBook.setPrice(new BigDecimal(priceParam));
            }

            if (isActiveParam != null) {
                existingBook.setActive(Boolean.parseBoolean(isActiveParam));
            }

            if (updateBook(existingBook)) {
                sendSuccessResponse(response, "Book updated successfully");
                LOGGER.info("Book updated: " + existingBook.getTitle());
            } else {
                sendErrorResponse(response, 500, "Failed to update book");
            }

        } catch (NumberFormatException e) {
            sendErrorResponse(response, 400, "Invalid number format in request parameters");
        } catch (IllegalArgumentException e) {
            sendErrorResponse(response, 400, "Invalid digital file type");
        }
    }

    /**
     * Handle delete book (soft delete)
     */
    private void handleDeleteBook(HttpServletRequest request, HttpServletResponse response, int bookId)
            throws IOException {

        Book book = getBookById(bookId);
        if (book == null) {
            sendErrorResponse(response, 404, "Book not found");
            return;
        }

        // Check if book has active borrows
        if (hasActiveBorrows(bookId)) {
            sendErrorResponse(response, 400, "Cannot delete book with active borrows");
            return;
        }

        if (deleteBook(bookId)) {
            sendSuccessResponse(response, "Book deleted successfully");
            LOGGER.info("Book deleted: " + book.getTitle());
        } else {
            sendErrorResponse(response, 500, "Failed to delete book");
        }
    }

    /**
     * Get all books with optional filtering
     */
    private List<Book> getAllBooks(String genre, String author, String availableOnly,
                                  String digitalOnly, int limit, int offset) {
        List<Book> books = new ArrayList<>();
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();

            StringBuilder sql = new StringBuilder("SELECT * FROM books WHERE is_active = TRUE");
            List<Object> parameters = new ArrayList<>();

            if (genre != null && !genre.trim().isEmpty()) {
                sql.append(" AND genre = ?");
                parameters.add(genre.trim());
            }

            if (author != null && !author.trim().isEmpty()) {
                sql.append(" AND author LIKE ?");
                parameters.add("%" + author.trim() + "%");
            }

            if ("true".equals(availableOnly)) {
                sql.append(" AND (available_copies > 0 OR digital_available = TRUE)");
            }

            if ("true".equals(digitalOnly)) {
                sql.append(" AND digital_available = TRUE");
            }

            sql.append(" ORDER BY title LIMIT ? OFFSET ?");
            parameters.add(limit);
            parameters.add(offset);

            statement = connection.prepareStatement(sql.toString());

            for (int i = 0; i < parameters.size(); i++) {
                statement.setObject(i + 1, parameters.get(i));
            }

            resultSet = statement.executeQuery();

            while (resultSet.next()) {
                books.add(mapResultSetToBook(resultSet));
            }

        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get all books", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return books;
    }

    /**
     * Get book by ID
     */
    private Book getBookById(int bookId) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT * FROM books WHERE book_id = ? AND is_active = TRUE";
            statement = connection.prepareStatement(sql);
            statement.setInt(1, bookId);
            resultSet = statement.executeQuery();

            if (resultSet.next()) {
                return mapResultSetToBook(resultSet);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get book by ID", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return null;
    }

    /**
     * Create a new book
     */
    private boolean createBook(Book book) {
        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DBConnection.getConnection();
            String sql = """
                INSERT INTO books (isbn, title, author, genre, publisher, publication_year,
                description, physical_copies, available_copies, digital_available,
                digital_file_path, digital_file_type, cover_image_path, price)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

            statement = connection.prepareStatement(sql);
            statement.setString(1, book.getIsbn());
            statement.setString(2, book.getTitle());
            statement.setString(3, book.getAuthor());
            statement.setString(4, book.getGenre());
            statement.setString(5, book.getPublisher());
            statement.setInt(6, book.getPublicationYear());
            statement.setString(7, book.getDescription());
            statement.setInt(8, book.getPhysicalCopies());
            statement.setInt(9, book.getAvailableCopies());
            statement.setBoolean(10, book.isDigitalAvailable());
            statement.setString(11, book.getDigitalFilePath());
            statement.setString(12, book.getDigitalFileType() != null ? book.getDigitalFileType().toString() : null);
            statement.setString(13, book.getCoverImagePath());
            statement.setBigDecimal(14, book.getPrice());

            return statement.executeUpdate() > 0;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to create book", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }

    /**
     * Update an existing book
     */
    private boolean updateBook(Book book) {
        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DBConnection.getConnection();
            String sql = """
                UPDATE books SET isbn = ?, title = ?, author = ?, genre = ?, publisher = ?,
                publication_year = ?, description = ?, physical_copies = ?, available_copies = ?,
                digital_available = ?, digital_file_path = ?, digital_file_type = ?,
                cover_image_path = ?, price = ?, is_active = ?
                WHERE book_id = ?
                """;

            statement = connection.prepareStatement(sql);
            statement.setString(1, book.getIsbn());
            statement.setString(2, book.getTitle());
            statement.setString(3, book.getAuthor());
            statement.setString(4, book.getGenre());
            statement.setString(5, book.getPublisher());
            statement.setInt(6, book.getPublicationYear());
            statement.setString(7, book.getDescription());
            statement.setInt(8, book.getPhysicalCopies());
            statement.setInt(9, book.getAvailableCopies());
            statement.setBoolean(10, book.isDigitalAvailable());
            statement.setString(11, book.getDigitalFilePath());
            statement.setString(12, book.getDigitalFileType() != null ? book.getDigitalFileType().toString() : null);
            statement.setString(13, book.getCoverImagePath());
            statement.setBigDecimal(14, book.getPrice());
            statement.setBoolean(15, book.isActive());
            statement.setInt(16, book.getBookId());

            return statement.executeUpdate() > 0;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to update book", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }

    /**
     * Delete a book (soft delete)
     */
    private boolean deleteBook(int bookId) {
        Connection connection = null;
        PreparedStatement statement = null;

        try {
            connection = DBConnection.getConnection();
            String sql = "UPDATE books SET is_active = FALSE WHERE book_id = ?";
            statement = connection.prepareStatement(sql);
            statement.setInt(1, bookId);

            return statement.executeUpdate() > 0;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to delete book", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }

    /**
     * Check if book has active borrows
     */
    private boolean hasActiveBorrows(int bookId) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT COUNT(*) FROM borrow_records WHERE book_id = ? AND status IN ('BORROWED', 'OVERDUE')";
            statement = connection.prepareStatement(sql);
            statement.setInt(1, bookId);
            resultSet = statement.executeQuery();

            if (resultSet.next()) {
                return resultSet.getInt(1) > 0;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to check active borrows", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }

        return false;
    }

    /**
     * Map ResultSet to Book object
     */
    private Book mapResultSetToBook(ResultSet resultSet) throws SQLException {
        Book book = new Book();
        book.setBookId(resultSet.getInt("book_id"));
        book.setIsbn(resultSet.getString("isbn"));
        book.setTitle(resultSet.getString("title"));
        book.setAuthor(resultSet.getString("author"));
        book.setGenre(resultSet.getString("genre"));
        book.setPublisher(resultSet.getString("publisher"));
        book.setPublicationYear(resultSet.getInt("publication_year"));
        book.setDescription(resultSet.getString("description"));
        book.setPhysicalCopies(resultSet.getInt("physical_copies"));
        book.setAvailableCopies(resultSet.getInt("available_copies"));
        book.setDigitalAvailable(resultSet.getBoolean("digital_available"));
        book.setDigitalFilePath(resultSet.getString("digital_file_path"));

        String digitalFileType = resultSet.getString("digital_file_type");
        if (digitalFileType != null) {
            book.setDigitalFileType(Book.DigitalFileType.valueOf(digitalFileType));
        }

        book.setCoverImagePath(resultSet.getString("cover_image_path"));
        book.setPrice(resultSet.getBigDecimal("price"));
        book.setActive(resultSet.getBoolean("is_active"));
        book.setCreatedAt(resultSet.getTimestamp("created_at"));
        book.setUpdatedAt(resultSet.getTimestamp("updated_at"));

        return book;
    }

    /**
     * Send success response
     */
    private void sendSuccessResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        PrintWriter out = response.getWriter();
        out.print("{\"success\": true, \"message\": \"" + message + "\"}");
        out.flush();
    }

    /**
     * Send book response
     */
    private void sendBookResponse(HttpServletResponse response, String message, Book book) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        PrintWriter out = response.getWriter();

        StringBuilder json = new StringBuilder();
        json.append("{\"success\": true, \"message\": \"").append(message).append("\", \"book\": ");
        json.append(bookToJson(book));
        json.append("}");

        out.print(json.toString());
        out.flush();
    }

    /**
     * Send books response
     */
    private void sendBooksResponse(HttpServletResponse response, String message, List<Book> books) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        PrintWriter out = response.getWriter();

        StringBuilder json = new StringBuilder();
        json.append("{\"success\": true, \"message\": \"").append(message).append("\", \"books\": [");

        for (int i = 0; i < books.size(); i++) {
            json.append(bookToJson(books.get(i)));
            if (i < books.size() - 1) {
                json.append(",");
            }
        }

        json.append("]}");
        out.print(json.toString());
        out.flush();
    }

    /**
     * Convert book to JSON string
     */
    private String bookToJson(Book book) {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"bookId\": ").append(book.getBookId()).append(",");
        json.append("\"isbn\": \"").append(book.getIsbn() != null ? book.getIsbn() : "").append("\",");
        json.append("\"title\": \"").append(book.getTitle()).append("\",");
        json.append("\"author\": \"").append(book.getAuthor()).append("\",");
        json.append("\"genre\": \"").append(book.getGenre() != null ? book.getGenre() : "").append("\",");
        json.append("\"publisher\": \"").append(book.getPublisher() != null ? book.getPublisher() : "").append("\",");
        json.append("\"publicationYear\": ").append(book.getPublicationYear()).append(",");
        json.append("\"description\": \"").append(book.getDescription() != null ? book.getDescription() : "").append("\",");
        json.append("\"physicalCopies\": ").append(book.getPhysicalCopies()).append(",");
        json.append("\"availableCopies\": ").append(book.getAvailableCopies()).append(",");
        json.append("\"digitalAvailable\": ").append(book.isDigitalAvailable()).append(",");
        json.append("\"digitalFilePath\": \"").append(book.getDigitalFilePath() != null ? book.getDigitalFilePath() : "").append("\",");
        json.append("\"digitalFileType\": \"").append(book.getDigitalFileType() != null ? book.getDigitalFileType().toString() : "").append("\",");
        json.append("\"coverImagePath\": \"").append(book.getCoverImagePath() != null ? book.getCoverImagePath() : "").append("\",");
        json.append("\"price\": ").append(book.getPrice() != null ? book.getPrice() : "0").append(",");
        json.append("\"isActive\": ").append(book.isActive()).append(",");
        json.append("\"availabilityStatus\": \"").append(book.getAvailabilityStatus()).append("\"");
        json.append("}");
        return json.toString();
    }

    /**
     * Send error response
     */
    private void sendErrorResponse(HttpServletResponse response, int statusCode, String message) throws IOException {
        response.setStatus(statusCode);
        PrintWriter out = response.getWriter();
        out.print("{\"success\": false, \"message\": \"" + message + "\"}");
        out.flush();
    }
}
