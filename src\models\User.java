package models;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * User model class representing library users and administrators
 */
public class User {
    
    public enum Role {
        ADMIN, USER
    }
    
    private int userId;
    private String username;
    private String password;
    private String email;
    private String fullName;
    private Role role;
    private String phone;
    private String address;
    private Date membershipDate;
    private boolean isActive;
    private Timestamp createdAt;
    private Timestamp updatedAt;
    
    // Default constructor
    public User() {
        this.role = Role.USER;
        this.isActive = true;
        this.membershipDate = new Date(System.currentTimeMillis());
    }
    
    // Constructor with essential fields
    public User(String username, String password, String email, String fullName) {
        this();
        this.username = username;
        this.password = password;
        this.email = email;
        this.fullName = fullName;
    }
    
    // Constructor with role
    public User(String username, String password, String email, String fullName, Role role) {
        this(username, password, email, fullName);
        this.role = role;
    }
    
    // Full constructor
    public User(int userId, String username, String password, String email, String fullName, 
                Role role, String phone, String address, Date membershipDate, boolean isActive,
                Timestamp createdAt, Timestamp updatedAt) {
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.email = email;
        this.fullName = fullName;
        this.role = role;
        this.phone = phone;
        this.address = address;
        this.membershipDate = membershipDate;
        this.isActive = isActive;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Getters and Setters
    public int getUserId() {
        return userId;
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public Role getRole() {
        return role;
    }
    
    public void setRole(Role role) {
        this.role = role;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public Date getMembershipDate() {
        return membershipDate;
    }
    
    public void setMembershipDate(Date membershipDate) {
        this.membershipDate = membershipDate;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public Timestamp getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }
    
    public Timestamp getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Utility methods
    public boolean isAdmin() {
        return role == Role.ADMIN;
    }
    
    public boolean isUser() {
        return role == Role.USER;
    }
    
    /**
     * Check if user credentials are valid (basic validation)
     * @return true if valid, false otherwise
     */
    public boolean isValidForRegistration() {
        return username != null && !username.trim().isEmpty() &&
               password != null && password.length() >= 6 &&
               email != null && email.contains("@") &&
               fullName != null && !fullName.trim().isEmpty();
    }
    
    /**
     * Get display name for the user
     * @return formatted display name
     */
    public String getDisplayName() {
        return fullName != null ? fullName : username;
    }
    
    /**
     * Get role display string
     * @return role as string
     */
    public String getRoleDisplay() {
        return role.toString().toLowerCase();
    }
    
    @Override
    public String toString() {
        return "User{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", fullName='" + fullName + '\'' +
                ", role=" + role +
                ", isActive=" + isActive +
                ", membershipDate=" + membershipDate +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        User user = (User) obj;
        return userId == user.userId;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(userId);
    }
}
