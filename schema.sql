-- Library Management System Database Schema
-- MySQL Database Schema

CREATE DATABASE IF NOT EXISTS library_management;
USE library_management;

-- Users table with role-based access
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    role ENUM('ADMIN', 'USER') DEFAULT 'USER',
    phone VARCHAR(15),
    address TEXT,
    membership_date DATE DEFAULT (CURRENT_DATE),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Books table with physical and digital support
CREATE TABLE books (
    book_id INT PRIMARY KEY AUTO_INCREMENT,
    isbn VARCHAR(20) UNIQUE,
    title VARCHAR(255) NOT NULL,
    author <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    genre VARCHAR(100),
    publisher <PERSON><PERSON><PERSON><PERSON>(100),
    publication_year YEAR,
    description TEXT,
    physical_copies INT DEFAULT 0,
    available_copies INT DEFAULT 0,
    digital_available BOOLEAN DEFAULT FALSE,
    digital_file_path VARCHAR(500),
    digital_file_type ENUM('PDF', 'EPUB', 'MOBI'),
    cover_image_path VARCHAR(500),
    price DECIMAL(10,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Borrow records for physical books
CREATE TABLE borrow_records (
    record_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    book_id INT NOT NULL,
    borrow_date DATE NOT NULL,
    due_date DATE NOT NULL,
    return_date DATE NULL,
    status ENUM('BORROWED', 'RETURNED', 'OVERDUE') DEFAULT 'BORROWED',
    fine_amount DECIMAL(10,2) DEFAULT 0.00,
    fine_paid BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
);

-- Digital access records for e-books
CREATE TABLE digital_access (
    access_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    book_id INT NOT NULL,
    access_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reading_progress INT DEFAULT 0, -- percentage or page number
    bookmarks TEXT, -- JSON string of bookmarks
    download_count INT DEFAULT 0,
    is_downloaded BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_book (user_id, book_id)
);

-- Fines table for better tracking
CREATE TABLE fines (
    fine_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    record_id INT NOT NULL,
    fine_amount DECIMAL(10,2) NOT NULL,
    fine_reason VARCHAR(255),
    fine_date DATE NOT NULL,
    paid_date DATE NULL,
    is_paid BOOLEAN DEFAULT FALSE,
    payment_method VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (record_id) REFERENCES borrow_records(record_id) ON DELETE CASCADE
);

-- User preferences for recommendations
CREATE TABLE user_preferences (
    preference_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    preferred_genres TEXT, -- JSON array of genres
    reading_mood VARCHAR(100),
    favorite_authors TEXT, -- JSON array of authors
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Book ratings and reviews
CREATE TABLE book_ratings (
    rating_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    book_id INT NOT NULL,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_book_rating (user_id, book_id)
);

-- Book recommendations tracking
CREATE TABLE recommendations (
    recommendation_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    book_id INT NOT NULL,
    recommendation_type ENUM('GENRE_BASED', 'HISTORY_BASED', 'MOOD_BASED', 'TRENDING'),
    recommendation_score DECIMAL(5,2),
    is_clicked BOOLEAN DEFAULT FALSE,
    is_borrowed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
);

-- System settings
CREATE TABLE system_settings (
    setting_id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('BORROW_DURATION_DAYS', '14', 'Default borrowing duration in days'),
('FINE_PER_DAY', '2.00', 'Fine amount per day for overdue books'),
('MAX_BOOKS_PER_USER', '5', 'Maximum books a user can borrow at once'),
('DIGITAL_ACCESS_DURATION_DAYS', '30', 'Digital book access duration in days');

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password, email, full_name, role) VALUES
('admin', 'admin123', '<EMAIL>', 'System Administrator', 'ADMIN');

-- Sample books data
INSERT INTO books (isbn, title, author, genre, publisher, publication_year, description, physical_copies, available_copies, digital_available, price) VALUES
('978-0-7432-7356-5', 'The Da Vinci Code', 'Dan Brown', 'Mystery', 'Doubleday', 2003, 'A mystery thriller novel', 5, 5, TRUE, 15.99),
('978-0-06-112008-4', 'To Kill a Mockingbird', 'Harper Lee', 'Fiction', 'J.B. Lippincott & Co.', 1960, 'A classic American novel', 3, 3, TRUE, 12.99),
('978-0-452-28423-4', '1984', 'George Orwell', 'Dystopian Fiction', 'Secker & Warburg', 1949, 'A dystopian social science fiction novel', 4, 4, TRUE, 13.99),
('978-0-7432-4722-4', 'The Catcher in the Rye', 'J.D. Salinger', 'Fiction', 'Little, Brown and Company', 1951, 'A coming-of-age story', 2, 2, FALSE, 11.99),
('978-0-06-085052-4', 'Where the Crawdads Sing', 'Delia Owens', 'Mystery', 'G.P. Putnams Sons', 2018, 'A mystery and coming-of-age story', 6, 6, TRUE, 16.99);

-- Create indexes for better performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_books_title ON books(title);
CREATE INDEX idx_books_author ON books(author);
CREATE INDEX idx_books_genre ON books(genre);
CREATE INDEX idx_books_isbn ON books(isbn);
CREATE INDEX idx_borrow_records_user_id ON borrow_records(user_id);
CREATE INDEX idx_borrow_records_book_id ON borrow_records(book_id);
CREATE INDEX idx_borrow_records_status ON borrow_records(status);
CREATE INDEX idx_borrow_records_due_date ON borrow_records(due_date);
CREATE INDEX idx_digital_access_user_id ON digital_access(user_id);
CREATE INDEX idx_digital_access_book_id ON digital_access(book_id);
CREATE INDEX idx_fines_user_id ON fines(user_id);
CREATE INDEX idx_fines_is_paid ON fines(is_paid);
