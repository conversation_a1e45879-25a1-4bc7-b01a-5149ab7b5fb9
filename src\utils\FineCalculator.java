package utils;

import models.BorrowRecord;
import models.Fine;
import db.DBConnection;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Utility class for calculating fines based on overdue books
 */
public class FineCalculator {
    
    private static final Logger LOGGER = Logger.getLogger(FineCalculator.class.getName());
    
    // Default fine settings
    private static final BigDecimal DEFAULT_FINE_PER_DAY = new BigDecimal("2.00");
    private static final int GRACE_PERIOD_DAYS = 1; // 1 day grace period
    private static final BigDecimal MAX_FINE_AMOUNT = new BigDecimal("50.00");
    
    /**
     * Calculate fine for a borrow record
     * @param borrowRecord the borrow record to calculate fine for
     * @return calculated fine amount
     */
    public static BigDecimal calculateFine(BorrowRecord borrowRecord) {
        if (borrowRecord == null || borrowRecord.getDueDate() == null) {
            return BigDecimal.ZERO;
        }
        
        // If book is already returned, no additional fine
        if (borrowRecord.isReturned()) {
            return borrowRecord.getFineAmount() != null ? borrowRecord.getFineAmount() : BigDecimal.ZERO;
        }
        
        LocalDate dueDate = borrowRecord.getDueDate().toLocalDate();
        LocalDate currentDate = LocalDate.now();
        
        // Calculate days overdue
        long daysOverdue = ChronoUnit.DAYS.between(dueDate, currentDate);
        
        // Apply grace period
        daysOverdue = Math.max(0, daysOverdue - GRACE_PERIOD_DAYS);
        
        if (daysOverdue <= 0) {
            return BigDecimal.ZERO;
        }
        
        // Get fine per day from system settings
        BigDecimal finePerDay = getFinePerDayFromSettings();
        
        // Calculate total fine
        BigDecimal totalFine = finePerDay.multiply(BigDecimal.valueOf(daysOverdue));
        
        // Apply maximum fine limit
        if (totalFine.compareTo(MAX_FINE_AMOUNT) > 0) {
            totalFine = MAX_FINE_AMOUNT;
        }
        
        return totalFine;
    }
    
    /**
     * Calculate fine for specific days overdue
     * @param daysOverdue number of days overdue
     * @return calculated fine amount
     */
    public static BigDecimal calculateFine(long daysOverdue) {
        if (daysOverdue <= GRACE_PERIOD_DAYS) {
            return BigDecimal.ZERO;
        }
        
        long effectiveDaysOverdue = daysOverdue - GRACE_PERIOD_DAYS;
        BigDecimal finePerDay = getFinePerDayFromSettings();
        BigDecimal totalFine = finePerDay.multiply(BigDecimal.valueOf(effectiveDaysOverdue));
        
        // Apply maximum fine limit
        if (totalFine.compareTo(MAX_FINE_AMOUNT) > 0) {
            totalFine = MAX_FINE_AMOUNT;
        }
        
        return totalFine;
    }
    
    /**
     * Get fine per day from system settings
     * @return fine per day amount
     */
    private static BigDecimal getFinePerDayFromSettings() {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT setting_value FROM system_settings WHERE setting_key = 'FINE_PER_DAY'";
            statement = connection.prepareStatement(sql);
            resultSet = statement.executeQuery();
            
            if (resultSet.next()) {
                String value = resultSet.getString("setting_value");
                return new BigDecimal(value);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Failed to get fine per day from settings, using default", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }
        
        return DEFAULT_FINE_PER_DAY;
    }
    
    /**
     * Update fine amount for a borrow record
     * @param borrowRecord the borrow record to update
     * @return true if update was successful
     */
    public static boolean updateFineForBorrowRecord(BorrowRecord borrowRecord) {
        if (borrowRecord == null) return false;
        
        BigDecimal newFineAmount = calculateFine(borrowRecord);
        borrowRecord.setFineAmount(newFineAmount);
        
        // Update in database
        Connection connection = null;
        PreparedStatement statement = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "UPDATE borrow_records SET fine_amount = ?, status = ? WHERE record_id = ?";
            statement = connection.prepareStatement(sql);
            statement.setBigDecimal(1, newFineAmount);
            
            // Update status if overdue
            if (borrowRecord.isOverdue() && !borrowRecord.isReturned()) {
                statement.setString(2, "OVERDUE");
                borrowRecord.setStatus(BorrowRecord.Status.OVERDUE);
            } else {
                statement.setString(2, borrowRecord.getStatus().toString());
            }
            
            statement.setInt(3, borrowRecord.getRecordId());
            
            int rowsUpdated = statement.executeUpdate();
            return rowsUpdated > 0;
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to update fine for borrow record", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }
    
    /**
     * Create a fine record in the fines table
     * @param borrowRecord the borrow record
     * @param fineAmount the fine amount
     * @param reason the reason for the fine
     * @return true if fine was created successfully
     */
    public static boolean createFineRecord(BorrowRecord borrowRecord, BigDecimal fineAmount, String reason) {
        if (borrowRecord == null || fineAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        Connection connection = null;
        PreparedStatement statement = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "INSERT INTO fines (user_id, record_id, fine_amount, fine_reason, fine_date) VALUES (?, ?, ?, ?, CURDATE())";
            statement = connection.prepareStatement(sql);
            statement.setInt(1, borrowRecord.getUserId());
            statement.setInt(2, borrowRecord.getRecordId());
            statement.setBigDecimal(3, fineAmount);
            statement.setString(4, reason);
            
            int rowsInserted = statement.executeUpdate();
            return rowsInserted > 0;
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to create fine record", e);
            return false;
        } finally {
            DBConnection.closeResources(connection, statement);
        }
    }
    
    /**
     * Calculate total unpaid fines for a user
     * @param userId the user ID
     * @return total unpaid fine amount
     */
    public static BigDecimal getTotalUnpaidFines(int userId) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT COALESCE(SUM(fine_amount), 0) as total_fines FROM fines WHERE user_id = ? AND is_paid = FALSE";
            statement = connection.prepareStatement(sql);
            statement.setInt(1, userId);
            resultSet = statement.executeQuery();
            
            if (resultSet.next()) {
                return resultSet.getBigDecimal("total_fines");
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to get total unpaid fines", e);
        } finally {
            DBConnection.closeResources(connection, statement, resultSet);
        }
        
        return BigDecimal.ZERO;
    }
    
    /**
     * Check if user has any unpaid fines
     * @param userId the user ID
     * @return true if user has unpaid fines
     */
    public static boolean hasUnpaidFines(int userId) {
        BigDecimal totalFines = getTotalUnpaidFines(userId);
        return totalFines.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * Update all overdue records and calculate fines
     * @return number of records updated
     */
    public static int updateAllOverdueRecords() {
        Connection connection = null;
        PreparedStatement selectStatement = null;
        ResultSet resultSet = null;
        int updatedCount = 0;
        
        try {
            connection = DBConnection.getConnection();
            String sql = "SELECT record_id, user_id, book_id, borrow_date, due_date, fine_amount " +
                        "FROM borrow_records WHERE status = 'BORROWED' AND due_date < CURDATE()";
            selectStatement = connection.prepareStatement(sql);
            resultSet = selectStatement.executeQuery();
            
            while (resultSet.next()) {
                BorrowRecord record = new BorrowRecord();
                record.setRecordId(resultSet.getInt("record_id"));
                record.setUserId(resultSet.getInt("user_id"));
                record.setBookId(resultSet.getInt("book_id"));
                record.setBorrowDate(resultSet.getDate("borrow_date"));
                record.setDueDate(resultSet.getDate("due_date"));
                record.setFineAmount(resultSet.getBigDecimal("fine_amount"));
                record.setStatus(BorrowRecord.Status.BORROWED);
                
                if (updateFineForBorrowRecord(record)) {
                    updatedCount++;
                }
            }
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to update overdue records", e);
        } finally {
            DBConnection.closeResources(connection, selectStatement, resultSet);
        }
        
        return updatedCount;
    }
    
    /**
     * Get fine calculation summary
     * @param borrowRecord the borrow record
     * @return formatted string with fine details
     */
    public static String getFineCalculationSummary(BorrowRecord borrowRecord) {
        if (borrowRecord == null) return "No record provided";
        
        long daysOverdue = borrowRecord.getDaysOverdue();
        BigDecimal fineAmount = calculateFine(borrowRecord);
        BigDecimal finePerDay = getFinePerDayFromSettings();
        
        StringBuilder summary = new StringBuilder();
        summary.append("Fine Calculation Summary:\n");
        summary.append("Days Overdue: ").append(daysOverdue).append("\n");
        summary.append("Grace Period: ").append(GRACE_PERIOD_DAYS).append(" day(s)\n");
        summary.append("Effective Days: ").append(Math.max(0, daysOverdue - GRACE_PERIOD_DAYS)).append("\n");
        summary.append("Fine Per Day: $").append(finePerDay).append("\n");
        summary.append("Total Fine: $").append(fineAmount).append("\n");
        
        if (fineAmount.compareTo(MAX_FINE_AMOUNT) >= 0) {
            summary.append("(Maximum fine limit applied)");
        }
        
        return summary.toString();
    }
}
